# Specifies intentionally untracked files to ignore when using Git
# http://git-scm.com/docs/gitignore

*~
*.sw[mnpcod]
.tmp
*.tmp
*.tmp.*
$RECYCLE.BIN/

*.log
log.txt

# Ionic
/.ionic
/.sass-cache
/.sourcemaps
/.versions
/coverage
/dist
/node_modules
/platforms
/plugins
/www

# Capacitor
android/.gradle/
android/app/build/
android/build/
android/captures/
android/.idea/
android/local.properties

android/build.gradle
android/keystore.properties
android/app/sihaah-release-key.keystore

ios/Pods/
ios/build/
ios/DerivedData/

electron/node_modules/
electron/build/

# Compiled output
/tmp
/out-tsc
/bazel-out

# IDEs and editors
/.idea
/.vscode
*.sublime-project
*.sublime-workspace

# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*


# Miscellaneous
/.angular/cache
.DS_Store
Thumbs.db
UserInterfaceState.xcuserstate

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment
.env
.env.*
!env!
