/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{html,ts,scss}'],
  theme: {
    extend: {
      colors: {
        'brand-primary': '#003C44',
        'brand-secondary': '#FDF5E8',
      },
      backgroundImage: {
        'light-gradient-1': 'linear-gradient(135deg, #FDF5E8, #f5f0e5)',
        'light-gradient-2': 'linear-gradient(135deg, #f8f5f2, #FDF5E8)',
        'light-gradient-3': 'linear-gradient(135deg, #FDF5E8, #f8f3e0)',
        'light-gradient-4': 'linear-gradient(135deg, #f2f7f5, #FDF5E8)',
        'dark-gradient-1': 'linear-gradient(135deg, #003C44, #002a30)',
        'dark-gradient-2': 'linear-gradient(135deg, #003C44, #001a1e)',
      },
      boxShadow: {
        'light-card': '0 8px 20px rgba(0, 60, 68, 0.08)',
        'dark-card': '0 8px 30px rgba(0, 0, 0, 0.3)',
      },
      fontFamily: {
        'amiri': ['<PERSON><PERSON>', 'serif'],
      },
    },
  },
  plugins: [],
}
