/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */
@import '@ionic/angular/css/palettes/dark.system.css';

/* Theme Variables */

/* Import Tailwind directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Thème clair unique */
.light {
  --ion-background-color: #FDF5E8;
  --ion-background-color-shade: #f5e8d8;
  --ion-text-color: #003C44;
  --card-background: #FDF5E8;
  --card-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  --control-background: rgba(255, 255, 255, 0.8);
  --control-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Thème sombre unique */
.dark {
  --ion-background-color: #003C44;
  --ion-background-color-shade: #001a1e;
  --ion-text-color: #FDF5E8;
  --card-background: #003C44;
  --card-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
  --control-background: rgba(0, 60, 68, 0.7);
  --control-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

/* Supprimer les autres thèmes */

/* Font adjustments for Arabic */
[lang="ar"] {
  .sanad, .matn {
    font-family: 'Amiri', serif !important;
  }

  .sanad {
    margin-bottom: 2rem !important; /* Increase spacing between title and hadeeth */
  }

  .matn {
    font-size: 2.2rem !important;
    line-height: 1.8 !important;
  }
}

/* Note: Les noms des classes CSS sont conservés pour compatibilité,
   mais les données correspondent maintenant à:
   - .sanad -> title (titre)
   - .matn -> hadeeth (texte principal)
   - .rawi -> attribution (attribution/source)
*/

/* Add smooth transitions between themes */
body {
  transition: background-color 0.3s ease, color 0.3s ease;
}

.hadith-content, .control-button {
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* Add iOS-style blur effects for controls */
.controls {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Add Apple-style animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.hadith-content {
  animation: fadeIn 0.5s ease-out;
}

/* Add subtle texture to backgrounds */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: var(--pattern-overlay);
  pointer-events: none;
  z-index: -1;
}

/* Add iOS-style safe area margins */
.hadith-container {
  padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
  padding-top: max(2rem, env(safe-area-inset-top));
  padding-bottom: max(2rem, env(safe-area-inset-bottom));
}

/* Ensure content respects system navigation bars */
ion-content {
  --padding-bottom: env(safe-area-inset-bottom);
}

/* Fix for Android navigation bar overlap */
ion-app {
  padding-bottom: var(--ion-safe-area-bottom, 0);
}

/* Adjust header to respect safe area */
ion-header {
  padding-top: var(--ion-safe-area-top, 0);
}

/* Ensure bottom navigation doesn't overlap with system UI */
ion-footer, ion-tabs, ion-tab-bar {
  padding-bottom: var(--ion-safe-area-bottom, 0);
}

/* Remove scrollbars */
::-webkit-scrollbar {
  display: none !important;
}

* {
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
}

/* Faster text transitions */
.sanad, .matn {
  transition: all 0.2s ease-out;
}

/* Prevent scrolling on body but allow passive event listeners to work */
html, body {
  overscroll-behavior: none;
  position: fixed;
  height: 100%;
  width: 100%;
}

/* Ensure content doesn't overflow but allows gestures */
ion-content {
  --overflow: auto;
  touch-action: auto;
}

/* Improve scrolling performance */
* {
  -webkit-overflow-scrolling: touch;
}

/* Fix for scrolling issues on iOS */
.scroll-content {
  overflow-y: auto !important;
}

/* Ensure scrollable elements work properly */
.scrollable {
  overflow-y: auto !important;
  -webkit-overflow-scrolling: touch !important;
  touch-action: auto !important;
}

.hadith-container {
  overflow: auto;
  touch-action: auto;
}

/* Improve animation performance */
.hadith-content {
  will-change: transform, opacity;
  animation: fadeIn 0.3s ease-out;
}

/* Ensure gradients are applied to the body */
body.light-1 {
  background-image: linear-gradient(135deg, #FDF5E8, #f5f0e5);
}

body.light-2 {
  background-image: linear-gradient(135deg, #f8f5f2, #FDF5E8);
}

body.light-3 {
  background-image: linear-gradient(135deg, #FDF5E8, #f8f3e0);
}

body.light-4 {
  background-image: linear-gradient(135deg, #f2f7f5, #FDF5E8);
}

body.dark-1 {
  background-image: linear-gradient(135deg, #003C44, #002a30);
}

body.dark-2 {
  background-image: linear-gradient(135deg, #003C44, #001a1e);
}

/* Override any Ionic background colors that might be interfering */
ion-content {
  --background: transparent !important;
}

ion-app, ion-content, ion-page, .ion-page, #root {
  background: transparent !important;
}

/* Assurer que les éléments scrollables fonctionnent correctement */
.scrollable,
.matn-container,
.matn {
  overflow-y: scroll !important;
  -webkit-overflow-scrolling: touch !important;
  touch-action: pan-y !important;
  z-index: 9999 !important;
  pointer-events: auto !important;
}

/* Désactiver le comportement de défilement sur le corps mais permettre aux éléments enfants de défiler */
body, html {
  overflow: hidden;
  position: fixed;
  height: 100%;
  width: 100%;
  touch-action: none;
}

/* Permettre le défilement sur les conteneurs spécifiques */
ion-content {
  --overflow: hidden;
}

/* Assurer que les éléments scrollables fonctionnent correctement */
#matn-scroll-container::-webkit-scrollbar {
  display: none !important;
}

/* Styles spécifiques pour résoudre les problèmes de défilement */
.scrollable-area {
  -webkit-overflow-scrolling: touch !important;
  overflow-y: auto !important;
  touch-action: auto !important;
  pointer-events: auto !important;
}
