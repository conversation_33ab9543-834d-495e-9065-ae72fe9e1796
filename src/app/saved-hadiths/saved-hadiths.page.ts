import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButton,
  IonIcon,
  IonList,
  IonItem,
  IonLabel,
  IonBackButton,
  IonButtons,
  IonItemSliding, IonItemOptions, IonItemOption
} from '@ionic/angular/standalone';
import { HadithService, Hadith } from '../services/hadith.service';
import { ThemeService } from '../services/theme.service';
import { Preferences } from '@capacitor/preferences';
import { Router } from '@angular/router';
import { addIcons } from 'ionicons';
import { arrowBackOutline, trashOutline, bookmarkOutline } from 'ionicons/icons';
import { Toast } from '@capacitor/toast';

@Component({
  selector: 'app-saved-hadiths',
  templateUrl: './saved-hadiths.page.html',
  styleUrls: ['./saved-hadiths.page.scss'],
  standalone: true,
  imports: [CommonModule, IonHeader, IonToolbar, IonTitle, IonContent, IonButton, IonIcon, IonList, IonItem, IonLabel, IonBackButton, IonButtons, IonItemSliding, IonItemOptions, IonItemOption],
})
export class SavedHadithsPage implements OnInit {
  savedHadiths: Hadith[] = [];
  // Définir currentLanguage comme un type union pour éviter l'erreur TS2322
  currentLanguage: 'ar' | 'fr' | 'en' = 'ar';

  constructor(
    private hadithService: HadithService,
    public themeService: ThemeService,
    public router: Router // Changer en public pour l'accès depuis le template
  ) {
    addIcons({
      arrowBackOutline,
      trashOutline,
      bookmarkOutline
    });
  }

  ngOnInit() {
    // Utiliser une assertion de type pour éviter l'erreur
    this.currentLanguage = this.hadithService.getLanguage() as 'ar' | 'fr' | 'en';
    this.loadSavedHadiths();
  }

  async loadSavedHadiths() {
    try {
      // Vider d'abord la liste actuelle
      this.savedHadiths = [];

      // Récupérer les IDs sauvegardés
      const { value } = await Preferences.get({ key: 'savedHadiths' });
      if (!value) return;

      const savedIds = JSON.parse(value) as number[];
      if (savedIds.length === 0) return;

      // Charger les hadiths correspondants
      const hadiths = await this.hadithService.getHadithsByIds(savedIds);

      // Vérifier que tous les IDs ont été trouvés
      // Si certains IDs n'ont pas été trouvés, mettre à jour la liste sauvegardée
      const foundIds = hadiths.map(h => h.id);
      const missingIds = savedIds.filter(id => !foundIds.includes(id));

      if (missingIds.length > 0) {
        console.log('Some saved hadiths were not found:', missingIds);
        // Mettre à jour la liste sauvegardée
        const newSavedIds = savedIds.filter(id => foundIds.includes(id));
        await Preferences.set({
          key: 'savedHadiths',
          value: JSON.stringify(newSavedIds)
        });
      }

      // Mettre à jour la liste locale
      this.savedHadiths = hadiths;
    } catch (error) {
      console.error('Error loading saved hadiths:', error);
    }
  }

  async removeHadith(hadithId: number, event: Event) {
    event.stopPropagation();

    try {
      // Récupérer la liste actuelle
      const { value } = await Preferences.get({ key: 'savedHadiths' });
      if (!value) return;

      // Filtrer l'ID à supprimer
      let savedIds = JSON.parse(value) as number[];
      const newSavedIds = savedIds.filter(id => id !== hadithId);

      // Sauvegarder la nouvelle liste
      await Preferences.set({
        key: 'savedHadiths',
        value: JSON.stringify(newSavedIds)
      });

      // Mettre à jour la liste locale
      this.savedHadiths = this.savedHadiths.filter(h => h.id !== hadithId);

      // Afficher un toast de confirmation
      await Toast.show({
        text: this.currentLanguage === 'ar' ? 'تم حذف الحديث' :
              this.currentLanguage === 'fr' ? 'Hadith supprimé' :
              'Hadith removed',
        duration: 'short',
        position: 'bottom'
      });
    } catch (error) {
      console.error('Error removing hadith:', error);
      // Afficher un message d'erreur
      await Toast.show({
        text: this.currentLanguage === 'ar' ? 'خطأ في حذف الحديث' :
              this.currentLanguage === 'fr' ? 'Erreur lors de la suppression' :
              'Error removing hadith',
        duration: 'short',
        position: 'bottom'
      });
    }
  }

  viewHadith(hadith: Hadith) {
    // Stocker l'ID du hadith à afficher
    this.hadithService.setHadithToView(hadith.id);

    // Naviguer vers la page d'accueil
    this.router.navigate(['/home']).then(() => {
      // Forcer directement le chargement du hadith sans vérification
      // Cela évite le message d'erreur et assure que le hadith est chargé
      setTimeout(() => {
        this.hadithService.forceLoadHadith(hadith.id);
      }, 300);
    });
  }

  // Ajouter une méthode pour naviguer vers la page d'accueil
  goToHome() {
    this.router.navigate(['/home']);
  }

  getHadithPreview(hadith: Hadith): string {
    const translation = hadith.translations[this.currentLanguage];
    const hadeeth = translation.hadeeth; // Utiliser hadeeth au lieu de matn

    // Limiter à 100 caractères et ajouter "..." si plus long
    return hadeeth.length > 100 ? hadeeth.substring(0, 100) + '...' : hadeeth;
  }

  // Ajouter une méthode ionViewWillEnter pour s'assurer que les données sont rechargées à chaque fois que la page est affichée
  ionViewWillEnter() {
    this.loadSavedHadiths();
  }
}
