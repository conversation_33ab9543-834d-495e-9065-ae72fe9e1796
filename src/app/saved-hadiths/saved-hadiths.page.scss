.saved-hadiths-container {
  padding: 16px;
  height: 100%;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  opacity: 0.7;

  .empty-icon {
    font-size: 80px;
    margin-bottom: 20px;
    color: var(--ion-color-medium);
  }

  p {
    font-size: 18px;
    margin-bottom: 20px;
  }
}

/* Styles pour les options de glissement */
ion-item-sliding {
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
}

ion-item {
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  --background: var(--ion-card-background);

  h2 {
    font-weight: 600;
    margin-bottom: 8px;
  }

  p {
    color: var(--ion-color-medium);
    white-space: normal;
  }
}

/* Styles spécifiques pour les options de glissement en fonction de la langue */
:host-context([dir="rtl"]) {
  ion-item-option {
    display: flex;
    flex-direction: row-reverse;
  }

  ion-item-options[side="end"] {
    justify-content: flex-start;
  }
}

:host-context([dir="ltr"]) {
  ion-item-options[side="end"] {
    justify-content: flex-end;
  }
}

/* Assurer que l'icône de la corbeille est bien visible */
ion-item-option {
  --padding-start: 1rem;
  --padding-end: 1rem;

  ion-icon {
    font-size: 1.25rem;
  }
}
