<!-- Utiliser le composant header -->
<app-header [title]="'Sihaah'"></app-header>

<!-- Utiliser le composant de modal de partage -->
<app-share-modal
  [show]="showShareModal"
  [currentLanguage]="currentLanguage"
  (close)="closeShareModal($event)"
  (shareImage)="shareAsImage()"
  (shareText)="shareAsText()">
</app-share-modal>

<!-- Conteneur caché pour la génération d'image -->
<app-hadith-image
  #hadithImage
  [currentHadith]="currentHadith"
  [currentLanguage]="currentLanguage">
</app-hadith-image>

<ion-content [fullscreen]="true" [scrollY]="false">
  <!-- Spinner de chargement -->
  <div class="loading-container" *ngIf="(!currentHadith && !connectionError) || isLoadingNewBatch">
    <ion-spinner name="crescent" class="loading-spinner"></ion-spinner>
    <p class="loading-text">
      <span *ngIf="currentLanguage === 'ar'">جاري تحميل الأحاديث...</span>
      <span *ngIf="currentLanguage === 'fr'">Chargement des hadiths...</span>
      <span *ngIf="currentLanguage === 'en'">Loading hadiths...</span>
    </p>
  </div>

  <!-- Utiliser le composant d'erreur de connexion -->
  <app-connection-error
    [show]="connectionError && !currentHadith"
    [currentLanguage]="currentLanguage"
    (retry)="retryLoading()">
  </app-connection-error>

  <!-- Conteneur de swipe intégré directement -->
  <div #swipeContainer class="swipe-container" [style.display]="connectionError ? 'none' : 'flex'">
    <!-- Zones de clic pour navigation - étendues à toute la zone hors matn -->
    <div *ngIf="canGoBack" class="click-zone click-zone-left" (click)="handleLeftZoneClick()"></div>
    <div class="click-zone click-zone-right" (click)="handleRightZoneClick()"></div>

    <!-- Contenu du hadith -->
    <div *ngIf="currentHadith" [attr.lang]="currentLanguage" class="hadith-content">
      <!-- Title (anciennement Sanad) toujours CENTRÉ en haut -->
      <div class="sanad">{{ currentHadith.translations[currentLanguage].title }}</div>

      <!-- Hadeeth (anciennement Matn) avec défilement vertical si nécessaire -->
      <div class="matn-container scrollable-area" id="matn-scroll-container">
        <div class="matn" [attr.lang]="currentLanguage">{{ currentHadith.translations[currentLanguage].hadeeth }}</div>
      </div>

      <!-- Attribution (anciennement Rawi) toujours CENTRÉ en bas -->
      <div *ngIf="currentHadith.translations[currentLanguage].attribution" class="rawi">
        {{ currentHadith.translations[currentLanguage].attribution }}
      </div>
    </div>

    <!-- Animation de bookmark qui apparaît lors du double-clic -->
    <div *ngIf="showBookmarkAnimation" class="bookmark-animation">
      <ion-icon name="bookmark"></ion-icon>
    </div>
  </div>

  <!-- Ajouter juste après le swipe-container -->
  <div class="swipe-hint" *ngIf="showSwipeHint && !connectionError && currentHadith" [@fadeInOut]>
    <div class="swipe-hint-content">
      <div class="swipe-arrow left">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </div>
      <div class="swipe-text">
        <span *ngIf="currentLanguage === 'ar'">اسحب للتنقل بين الأحاديث</span>
        <span *ngIf="currentLanguage === 'fr'">Glissez pour naviguer</span>
        <span *ngIf="currentLanguage === 'en'">Swipe to navigate</span>
      </div>
      <div class="swipe-arrow right">
        <ion-icon name="chevron-forward-outline"></ion-icon>
      </div>
    </div>
  </div>

  <!-- Utiliser le composant FAB -->
  <app-fab-menu [hidden]="showShareModal"></app-fab-menu>
</ion-content>
