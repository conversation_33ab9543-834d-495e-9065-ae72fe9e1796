import {
  Component,
  OnInit,
  ViewChild,
  HostBinding,
  NgZone,
  AfterViewInit,
  OnDestroy,
  Renderer2,
  ElementRef
} from '@angular/core';
import {
  IonContent, IonIcon, IonSpinner
} from '@ionic/angular/standalone';
import {CommonModule} from '@angular/common';
import {HadithService} from '../services/hadith.service';
import {ThemeService} from '../services/theme.service';
import {addIcons} from 'ionicons';
import {
  languageOutline,
  moonOutline,
  sunnyOutline,
  chevronBackOutline,
  chevronForwardOutline,
  shareOutline,
  imageOutline,
  textOutline,
  closeOutline,
  shareSocialOutline,
  bookmarkOutline,
  bookmark,
  ellipsisVertical,
  cloudOfflineOutline
} from 'ionicons/icons';
import {GestureController, Gesture} from '@ionic/angular/standalone';
import {AnimationController} from '@ionic/angular/standalone';
import {Share} from '@capacitor/share';
import {Clipboard} from '@capacitor/clipboard';
import {Toast} from '@capacitor/toast';
import {Filesystem, Directory} from '@capacitor/filesystem';
import {Platform} from '@ionic/angular/standalone';
import html2canvas from 'html2canvas';
import {Preferences} from '@capacitor/preferences';
import {Router} from '@angular/router';
import {HadithImageComponent} from "../components/hadith-image/hadith-image.component";
import {HeaderComponent} from "../components/header/header.component";
import {FabMenuComponent} from "../components/fab-menu/fab-menu.component";
import {ShareModalComponent} from "../components/share-modal/share-modal.component";
import {ConnectionErrorComponent} from "../components/connection-error/connection-error.component";

import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    HadithImageComponent,
    HeaderComponent,
    FabMenuComponent,
    ShareModalComponent,
    ConnectionErrorComponent,
    IonContent,
    IonIcon,
    IonSpinner
  ],
  animations: [
    trigger('fadeInOut', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('500ms ease-in', style({ opacity: 1 }))
      ]),
      transition(':leave', [
        animate('500ms ease-out', style({ opacity: 0 }))
      ])
    ])
  ]
})
export class HomePage implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('swipeContainer') swipeContainer!: ElementRef;
  @ViewChild('hadithImage') hadithImage!: HadithImageComponent;
  @ViewChild(IonContent) content!: IonContent;
  @ViewChild('mainFab', { static: false }) mainFab: any;
  fabOpen = false;
  showSwipeHint: boolean = false;
  swipeHintTimer: any;

  @HostBinding('class.lang-ar') get isArabic() {
    return this.currentLanguage === 'ar';
  }

  @HostBinding('class.lang-fr') get isFrench() {
    return this.currentLanguage === 'fr';
  }

  @HostBinding('class.lang-en') get isEnglish() {
    return this.currentLanguage === 'en';
  }

  currentHadith: any = null;
  currentLanguage: string = 'ar'; // Set Arabic as default
  swipeGesture?: Gesture;
  hadithStats: { viewed: number, total: number } | null = null;
  showShareModal: boolean = false;
  isGeneratingImage: boolean = false;
  canGoBack: boolean = false;
  showBookmarkAnimation: boolean = false;
  savedHadiths: number[] = [];
  connectionError: boolean = false;
  isLoadingNewBatch: boolean = false;

  constructor(
    private hadithService: HadithService,
    public themeService: ThemeService,
    private gestureCtrl: GestureController,
    private animationCtrl: AnimationController,
    private zone: NgZone,
    private renderer: Renderer2,
    private platform: Platform,
    private router: Router  // Ajouter Router pour la navigation
  ) {
    addIcons({
      languageOutline,
      moonOutline,
      sunnyOutline,
      chevronBackOutline,
      chevronForwardOutline,
      shareOutline,
      shareSocialOutline,
      imageOutline,
      textOutline,
      closeOutline,
      bookmarkOutline,
      bookmark,
      ellipsisVertical,
      cloudOfflineOutline
    });

    // Écouter les événements des composants enfants
    window.addEventListener('shareHadith', this.handleShareHadith.bind(this));
    window.addEventListener('saveHadith', this.handleSaveHadith.bind(this));
    window.addEventListener('goToSavedHadiths', this.handleGoToSavedHadiths.bind(this));
    window.addEventListener('toggleLanguage', this.handleToggleLanguage.bind(this));
  }

  // Gestionnaires d'événements pour les composants enfants
  handleShareHadith(event: any) {
    this.shareHadith(event.detail);
  }

  handleSaveHadith(event: any) {
    this.saveHadith(event.detail);
  }

  handleGoToSavedHadiths(event: any) {
    this.goToSavedHadiths(event.detail);
  }

  handleToggleLanguage(event: any) {
    this.toggleLanguage(event.detail);
  }

  ngOnDestroy() {
    // Supprimer les écouteurs d'événements
    window.removeEventListener('shareHadith', this.handleShareHadith.bind(this));
    window.removeEventListener('saveHadith', this.handleSaveHadith.bind(this));
    window.removeEventListener('goToSavedHadiths', this.handleGoToSavedHadiths.bind(this));
    window.removeEventListener('toggleLanguage', this.handleToggleLanguage.bind(this));

    if (this.swipeGesture) {
      this.swipeGesture.destroy();
    }

    if (this.swipeHintTimer) {
      clearTimeout(this.swipeHintTimer);
    }
  }

  ngOnInit() {
    // Initialiser la langue
    this.currentLanguage = this.hadithService.getLanguage();

    // S'abonner aux changements de langue
    this.hadithService.languageChanged.subscribe(lang => {
      this.currentLanguage = lang;
    });

    // S'abonner aux erreurs réseau
    this.hadithService.networkError.subscribe(hasError => {
      console.log('Network error event received:', hasError);
      if (hasError) {
        // Afficher l'erreur de connexion seulement si nous n'avons pas encore de hadith
        // ou si nous essayons de charger un nouveau lot
        if (!this.currentHadith || this.isLoadingNewBatch) {
          this.connectionError = true;
        }
      } else {
        // Réinitialiser l'état d'erreur
        this.connectionError = false;
      }
    });

    // S'abonner au chargement des lots
    this.hadithService.batchLoading.subscribe(isLoading => {
      this.isLoadingNewBatch = isLoading;
    });

    // S'abonner aux changements de hadith (pour les hadiths chargés spécifiquement)
    this.hadithService.hadithChanged.subscribe(hadith => {
      console.log('Hadith changed event received:', hadith.id);
      this.currentHadith = hadith;
      this.updateHadithStats();
      this.updateCanGoBack();

      // Attendre que le DOM soit mis à jour puis réinitialiser le scroll
      setTimeout(() => {
        this.checkMatnOverflow();
        this.resetScrollPosition();
      }, 200);
    });

    // Vérifier si l'utilisateur a déjà vu l'indice de swipe
    this.checkFirstTimeUser();

    // Charger les hadiths sauvegardés
    this.loadSavedHadiths();

    // Charger les hadiths
    this.loadInitialHadith();
  }

  // Nouvelle méthode pour charger les hadiths sans jamais afficher d'erreur
  async loadHadithsWithoutError() {
    // Forcer l'état à non-erreur
    this.connectionError = false;

    // Essayer de charger les hadiths en boucle jusqu'à ce qu'on réussisse
    let success = false;
    let attempts = 0;

    while (!success && attempts < 10) {
      attempts++;
      console.log(`Tentative de chargement des hadiths #${attempts}`);

      try {
        // Attendre que les hadiths soient chargés
        await this.hadithService.loadHadiths();

        // Vérifier si des hadiths ont été chargés
        if (this.hadithService.getHadithsCount() > 0) {
          success = true;
          console.log('Hadiths chargés avec succès!');

          // Charger le premier hadith
          this.loadNextHadith();
        } else {
          // Attendre avant de réessayer
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      } catch (error) {
        console.error('Erreur lors du chargement des hadiths:', error);
        // Attendre avant de réessayer
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    // Si après 10 tentatives on n'a toujours pas réussi, afficher un hadith par défaut
    if (!success) {
      console.log('Impossible de charger les hadiths après plusieurs tentatives');
      // Créer un hadith par défaut
      this.currentHadith = {
        id: 0,
        translations: {
          ar: {
            title: 'حديث افتراضي',
            hadeeth: 'لا يمكن تحميل الأحاديث حاليًا. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.'
          },
          fr: {
            title: 'Hadith par défaut',
            hadeeth: 'Impossible de charger les hadiths pour le moment. Veuillez vérifier votre connexion internet et réessayer.'
          },
          en: {
            title: 'Default Hadith',
            hadeeth: 'Unable to load hadiths at this time. Please check your internet connection and try again.'
          }
        }
      };
    }
  }

  async checkFirstTimeUser() {
    const { value } = await Preferences.get({ key: 'hasSeenSwipeHint' });

    if (!value) {
      // Montrer l'indice après un court délai
      setTimeout(() => {
        this.showSwipeHint = true;

        // Ne pas masquer automatiquement après 5 secondes
        // L'indice sera masqué lors du premier swipe dans la méthode hideSwipeHint()
      }, 1500);

      // Ne pas marquer comme vu immédiatement
      // Ce sera fait dans hideSwipeHint() après le premier swipe
    }
  }

  ionViewWillEnter() {
    console.log('ionViewWillEnter called');
    // Vérifier s'il y a un hadith spécifique à charger
    this.checkAndLoadHadith();
  }

  // Méthode pour réinitialiser la position de scroll du matn-container
  resetScrollPosition() {
    // Essayer plusieurs fois avec différentes méthodes pour s'assurer que le scroll est réinitialisé
    const resetScroll = () => {
      if (this.swipeContainer) {
        const matnContainer = this.swipeContainer.nativeElement.querySelector('.matn-container');
        if (matnContainer) {
          // Méthode 1: scrollTop
          matnContainer.scrollTop = 0;

          // Méthode 2: scrollTo
          matnContainer.scrollTo(0, 0);

          // Méthode 3: style transform (au cas où)
          matnContainer.style.transform = 'translateY(0)';

          console.log('Scroll position reset to top - scrollTop:', matnContainer.scrollTop);
        } else {
          console.log('matn-container not found for scroll reset');
        }
      } else {
        console.log('swipeContainer not available for scroll reset');
      }
    };

    // Réinitialiser immédiatement
    resetScroll();

    // Réinitialiser après un court délai
    setTimeout(resetScroll, 50);

    // Réinitialiser avec requestAnimationFrame
    requestAnimationFrame(resetScroll);

    // Réinitialiser après un délai plus long
    setTimeout(resetScroll, 200);
  }

  ngAfterViewInit() {
    console.log('ngAfterViewInit called');

    // Attendre que la vue soit complètement initialisée avec un délai plus long
    setTimeout(() => {
      console.log('Timeout callback executed');
      console.log('swipeContainer exists:', !!this.swipeContainer);
      console.log('connectionError:', this.connectionError);

      // Vérifier si swipeContainer existe et si nous ne sommes pas en mode erreur
      if (this.swipeContainer && !this.connectionError) {
        console.log('Setting up swipe gesture');
        this.setupHorizontalSwipeGesture();
        this.checkMatnOverflow();
      } else {
        console.log('Skipping swipe gesture setup - container not available or in error mode');
        console.log('swipeContainer:', this.swipeContainer);

        // Si le conteneur existe mais que nous sommes en mode erreur, configurer quand même les gestes
        // pour qu'ils soient prêts lorsque l'erreur sera résolue
        if (this.swipeContainer && this.connectionError) {
          console.log('Container exists but in error mode, setting up gesture anyway');
          this.setupHorizontalSwipeGesture();
        }
      }
    }, 500); // Augmenter le délai à 500ms
  }

  setupHorizontalSwipeGesture() {
    console.log('setupHorizontalSwipeGesture called');

    // Vérifier si swipeContainer existe
    if (!this.swipeContainer) {
      console.log('swipeContainer is undefined, skipping gesture setup');
      return;
    }

    const element = this.swipeContainer.nativeElement;
    console.log('Element for gesture:', element);

    // Disable any existing gesture to avoid duplicates
    if (this.swipeGesture) {
      console.log('Destroying existing gesture');
      this.swipeGesture.destroy();
    }

    try {
      // Create a new gesture for horizontal swipe
      this.swipeGesture = this.gestureCtrl.create({
        el: element,
        threshold: 10,
        direction: 'x',
        gestureName: 'horizontal-swipe',
        passive: false,
        onStart: (ev) => {
          // Masquer l'indice dès qu'un swipe commence
          this.hideSwipeHint();

          // Vérifier si le toucher commence sur le matn-container ou le FAB
          const target = ev.event.target as HTMLElement;
          if (target.closest('#matn-scroll-container') || target.closest('.matn') || target.closest('#main-fab')) {
            return false;
          }

          // Adapter la logique selon la langue
          const isRTL = this.currentLanguage === 'ar';

          // Pour l'arabe (RTL): swipe gauche = précédent, swipe droite = suivant
          // Pour LTR: swipe droit = précédent, swipe gauche = suivant
          if ((isRTL && ev.deltaX < 0 && !this.canGoBack) || (!isRTL && ev.deltaX > 0 && !this.canGoBack)) {
            return false;
          }

          return true;
        },
        onMove: (ev) => {
          const isRTL = this.currentLanguage === 'ar';

          // Pour l'arabe (RTL): swipe gauche = précédent, swipe droite = suivant
          // Pour LTR: swipe droit = précédent, swipe gauche = suivant
          if ((isRTL && ev.deltaX < 0 && !this.canGoBack) || (!isRTL && ev.deltaX > 0 && !this.canGoBack)) {
            return;
          }

          // Translate content while swiping horizontally
          element.style.transform = `translateX(${ev.deltaX}px)`;
          element.style.transition = 'none';

          // Start fading out when swipe reaches threshold
          if (Math.abs(ev.deltaX) > 20) {
            const opacity = Math.max(0.3, 1 - (Math.abs(ev.deltaX) - 20) / 60);
            element.style.opacity = opacity.toString();
          }
        },
        onEnd: (ev) => {
          element.style.transition = '0.2s ease-out';
          const isRTL = this.currentLanguage === 'ar';

          if ((isRTL && ev.deltaX < -50 && this.canGoBack) || (!isRTL && ev.deltaX > 50 && this.canGoBack)) {
            // Swipe pour précédent (selon la langue)
            this.animateHorizontalTransition(isRTL ? 'left' : 'right', () => {
              this.previousHadith();
            });
          } else if ((isRTL && ev.deltaX > 50) || (!isRTL && ev.deltaX < -50)) {
            // Swipe pour suivant (selon la langue)
            this.animateHorizontalTransition(isRTL ? 'right' : 'left', () => {
              this.nextHadith();
            });
          } else {
            // Reset position
            element.style.transform = 'translateX(0)';
            element.style.opacity = '1';
          }
        }
      });

      console.log('Gesture created successfully');
      this.swipeGesture.enable(true);
      console.log('Gesture enabled');
    } catch (error) {
      console.error('Error creating gesture:', error);
    }
  }

  // Refonte complète de l'animation de transition horizontale
  animateHorizontalTransition(direction: 'left' | 'right', callback: () => void) {
    // Vérifier que le composant existe
    if (!this.swipeContainer) {
      console.error('SwipeContainer not found');
      callback();
      return;
    }

    const element = this.swipeContainer.nativeElement;

    // Phase 1: Animation de sortie
    const exitAnimation = (cb: () => void) => {
      console.log(`Phase 1: Exit animation (${direction})`);
      const exitDistance = direction === 'left' ? -window.innerWidth : window.innerWidth;

      element.style.transition = '0.3s ease-out';
      element.style.transform = `translateX(${exitDistance}px)`;
      element.style.opacity = '0';

      setTimeout(cb, 300); // Attendre la fin de l'animation
    };

    // Phase 2: Préparation de l'entrée
    const prepareEntry = (cb: () => void) => {
      console.log('Phase 2: Prepare entry');
      const entryDistance = direction === 'left' ? window.innerWidth : -window.innerWidth;

      element.style.transition = 'none';
      element.style.transform = `translateX(${entryDistance}px)`;

      // Utiliser requestAnimationFrame pour s'assurer que les changements sont appliqués
      requestAnimationFrame(cb);
    };

    // Phase 3: Exécution du callback (changement de hadith)
    const executeCallback = (cb: () => void) => {
      console.log('Phase 3: Execute callback (change hadith)');
      this.zone.run(() => {
        callback();
        // Utiliser requestAnimationFrame pour s'assurer que le DOM est mis à jour
        requestAnimationFrame(cb);
      });
    };

    // Phase 4: Animation d'entrée
    const entryAnimation = () => {
      console.log('Phase 4: Entry animation');
      element.style.transition = '0.3s ease-out';
      element.style.transform = 'translateX(0)';
      element.style.opacity = '1';
    };

    // Exécuter les phases en séquence
    exitAnimation(() => {
      prepareEntry(() => {
        executeCallback(() => {
          entryAnimation();
        });
      });
    });
  }

  loadNextHadith() {
    console.log('loadNextHadith called');
    this.isLoadingNewBatch = true;

    this.hadithService.getNextHadith().then(hadith => {
      this.isLoadingNewBatch = false;

      if (hadith) {
        this.currentHadith = hadith;
        this.connectionError = false; // Réinitialiser l'erreur si on a réussi
        console.log('Next hadith loaded:', this.currentHadith);
        this.updateHadithStats();

        // Attendre que le DOM soit mis à jour
        setTimeout(() => {
          console.log('loadNextHadith - DOM updated, resetting scroll');
          this.checkMatnOverflow();
          // Réinitialiser la position de scroll
          this.resetScrollPosition();
        }, 100);
      } else if (!this.currentHadith) {
        // Si nous n'avons pas encore de hadith, c'est probablement un problème de connexion
        this.connectionError = true;
      }
      // Si nous avons déjà un hadith mais que le chargement du suivant a échoué,
      // nous gardons l'actuel et n'affichons pas d'erreur
    }).catch(error => {
      console.error('Error loading next hadith:', error);
      this.isLoadingNewBatch = false;

      // Afficher l'erreur de connexion seulement si nous n'avons pas de hadith actuel
      if (!this.currentHadith) {
        this.connectionError = true;
      }
    });
  }

  nextHadith() {
    this.hideSwipeHint();
    console.log('nextHadith called');

    // Réinitialiser le scroll immédiatement
    this.resetScrollPosition();

    this.loadNextHadith();
    this.updateCanGoBack(); // Mettre à jour l'état après navigation
    this.logCurrentState(); // Ajouter le logging

    // Réinitialiser la position de scroll avec un délai approprié
    setTimeout(() => {
      this.resetScrollPosition();
    }, 100);
  }

  previousHadith() {
    this.hideSwipeHint();
    console.log('previousHadith called');

    // Réinitialiser le scroll immédiatement
    this.resetScrollPosition();

    // Vérifier si on peut revenir en arrière
    if (this.canGoBack) {
      this.hadithService.getPreviousHadith().then(previousHadith => {
        if (previousHadith) {
          this.currentHadith = previousHadith;
          this.updateHadithStats();
          this.updateCanGoBack(); // Mettre à jour l'état après navigation
          this.logCurrentState(); // Ajouter le logging

          // Attendre que le DOM soit mis à jour
          setTimeout(() => {
            this.checkMatnOverflow();
            // Réinitialiser la position de scroll
            this.resetScrollPosition();
          }, 100);
        }
      });
    } else {
      // Optionnel : afficher un toast pour indiquer qu'on ne peut pas revenir plus loin
      Toast.show({
        text: this.currentLanguage === 'ar' ? 'لا يمكن العودة أكثر' :
          this.currentLanguage === 'fr' ? 'Impossible de revenir plus loin' :
            'Cannot go back further',
        duration: 'short',
        position: 'bottom'
      });
    }
  }

  updateHadithStats() {
    console.log('updateHadithStats called');
    this.hadithStats = this.hadithService.getViewedHadithsStats();
    console.log('Hadith stats updated:', this.hadithStats);
  }

  // cycleToNextTheme() {
  //   const nextTheme = this.themeService.getNextTheme();
  //   this.themeService.setTheme(nextTheme);
  // }

  // cycleToPreviousTheme() {
  //   const prevTheme = this.themeService.getPreviousTheme();
  //   this.themeService.setTheme(prevTheme);
  // }

  toggleLanguage(event?: Event) {
    if (event) {
      event.stopPropagation();
    }

    const currentLang = this.hadithService.getLanguage();
    let nextLang: 'ar' | 'fr' | 'en';

    if (currentLang === 'ar') {
      nextLang = 'fr';
    } else if (currentLang === 'fr') {
      nextLang = 'en';
    } else {
      nextLang = 'ar';
    }

    this.hadithService.setLanguage(nextLang);
    this.currentLanguage = nextLang;

    // Vérifier l'overflow après le changement de langue
    setTimeout(() => {
      this.checkMatnOverflow();
      // Réinitialiser la position de scroll lors du changement de langue
      this.resetScrollPosition();
    }, 100);
  }

  toggleTheme(event?: Event) {
    if (event) {
      event.stopPropagation();
    }
    this.closeFab(); // Fermer le FAB
    this.themeService.toggleTheme();
  }

  checkMatnOverflow() {
    if (this.swipeContainer) {
      const matnContainer = this.swipeContainer.nativeElement.querySelector('.matn-container');
      if (matnContainer) {
        const hasOverflow = matnContainer.scrollHeight > matnContainer.clientHeight;

        // Ajouter une classe visuelle si le matn a besoin de défilement
        if (hasOverflow) {
          matnContainer.classList.add('has-overflow');
        } else {
          matnContainer.classList.remove('has-overflow');
        }

        // Réinitialiser le scroll au début de manière agressive
        matnContainer.scrollTop = 0;
        matnContainer.scrollTo(0, 0);
        matnContainer.style.transform = 'translateY(0)';

        console.log('checkMatnOverflow - scroll reset, scrollTop after reset:', matnContainer.scrollTop);

        // Activer explicitement le défilement
        matnContainer.style.overflowY = 'auto';
        matnContainer.style.touchAction = 'auto';
        matnContainer.style.pointerEvents = 'auto';

        // S'assurer que le matn peut recevoir des événements
        const matn = matnContainer.querySelector('.matn');
        if (matn) {
          matn.style.pointerEvents = 'auto';
          matn.style.touchAction = 'auto';
        }
      }
    }
  }

  // Nouvelles méthodes pour le partage
  shareHadith(event: Event) {
    event.stopPropagation();
    this.closeFab();
    this.showShareModal = true;
  }

  closeShareModal(event: Event) {
    event.stopPropagation();
    this.showShareModal = false;
  }

  async shareAsImage() {
    if (this.isGeneratingImage || !this.currentHadith) return;

    this.isGeneratingImage = true;
    this.showShareModal = false; // Ceci va réafficher le FAB

    try {
      await Toast.show({
        text: this.currentLanguage === 'ar' ? 'جاري إعداد الصورة...' :
              this.currentLanguage === 'fr' ? 'Préparation de l\'image...' :
              'Preparing image...',
        duration: 'short',
        position: 'center'
      });

      // Préparer le conteneur pour la capture
      const container = this.hadithImage.getImageContainer().nativeElement;

      // Appliquer le thème actuel au conteneur d'image avec les bonnes couleurs
      if (this.themeService.isDarkMode()) {
        container.classList.add('dark-theme');
        // Utiliser la couleur verte de l'application au lieu du gris
        container.style.backgroundColor = '#003C44';
      } else {
        container.classList.remove('dark-theme');
        container.style.backgroundColor = '#FDF5E8';
      }

      // Ajuster la hauteur du conteneur en fonction du contenu
      const matnElement = container.querySelector('.hadith-image-matn');
      const matnHeight = matnElement.scrollHeight;
      container.style.height = `${Math.max(800, matnHeight + 400)}px`; // Hauteur minimale + espace pour les autres éléments

      // Générer l'image avec html2canvas
      const canvas = await html2canvas(container, {
        scale: 2, // Meilleure qualité
        useCORS: true,
        allowTaint: true,
        backgroundColor: this.themeService.isDarkMode() ? '#003C44' : '#FDF5E8' // Utiliser les couleurs de l'app
      });

      // Convertir le canvas en blob
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((b) => resolve(b!), 'image/png', 0.95);
      });

      // Vérifier si on est sur mobile ou navigateur
      if (this.platform.is('capacitor')) {
        // Sur mobile (iOS/Android) - utiliser les APIs natives
        const fileName = `hadith_${Date.now()}.png`;
        const filePath = await this.saveImageToDevice(blob, fileName);

        // Partager l'image avec un texte adapté à la langue
        await Share.share({
          title: this.currentLanguage === 'ar' ? 'حديث من صحاح' :
                 this.currentLanguage === 'fr' ? 'Hadith de Sihaah' :
                 'Hadith from Sihaah',
          text: this.currentLanguage === 'ar' ? 'شاركت هذا الحديث من تطبيق صحاح' :
                this.currentLanguage === 'fr' ? 'J\'ai partagé ce hadith depuis l\'application Sihaah' :
                'I shared this hadith from the Sihaah app',
          url: filePath,
          dialogTitle: this.currentLanguage === 'ar' ? 'مشاركة هذا الحديث' :
                       this.currentLanguage === 'fr' ? 'Partager ce hadith' :
                       'Share this hadith'
        });
      } else {
        // Sur navigateur - télécharger l'image
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `hadith_sihaah_${Date.now()}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        // Informer l'utilisateur
        await Toast.show({
          text: this.currentLanguage === 'ar' ? 'تم تنزيل الصورة' :
                this.currentLanguage === 'fr' ? 'Image téléchargée' :
                'Image downloaded',
          duration: 'short',
          position: 'bottom'
        });
      }

    } catch (error) {
      console.error('Erreur lors du partage de l\'image:', error);
      await Toast.show({
        text: this.currentLanguage === 'ar' ? 'تعذر مشاركة الصورة' :
              this.currentLanguage === 'fr' ? 'Impossible de partager l\'image' :
              'Unable to share image',
        duration: 'long',
        position: 'bottom'
      });
    } finally {
      this.isGeneratingImage = false;
    }
  }

  async saveImageToDevice(blob: Blob, fileName: string): Promise<string> {
    // Convertir le blob en base64
    const reader = new FileReader();
    const base64Data = await new Promise<string>((resolve) => {
      reader.onloadend = () => resolve(reader.result as string);
      reader.readAsDataURL(blob);
    });

    // Extraire seulement la partie data du base64
    const base64String = base64Data.split(',')[1];

    // Sauvegarder le fichier
    const result = await Filesystem.writeFile({
      path: fileName,
      data: base64String,
      directory: Directory.Cache,
      recursive: true
    });

    return this.platform.is('android')
      ? result.uri
      : Filesystem.getUri({
        directory: Directory.Cache,
        path: fileName
      }).then(uriResult => uriResult.uri);
  }

  async shareAsText() {
    if (!this.currentHadith) return;

    this.showShareModal = false; // Ceci va réafficher le FAB

    try {
      const hadith = this.currentHadith;
      const translation = hadith.translations[this.currentLanguage];

      // Construire le texte complet du hadith
      let hadithText = `${translation.title}\n\n${translation.hadeeth}`;

      if (translation.attribution) {
        hadithText += `\n\n${translation.attribution}`;
      }

      // Ajouter la signature de l'application selon la langue
      hadithText += this.currentLanguage === 'ar'
        ? '\n\n- تم المشاركة من تطبيق صحاح'
        : this.currentLanguage === 'fr'
          ? '\n\n- Partagé depuis l\'application Sihaah'
          : '\n\n- Shared from the Sihaah app';

      // Copier le texte dans le presse-papier
      await Clipboard.write({
        string: hadithText
      });

      // Afficher un message de confirmation
      await Toast.show({
        text: this.currentLanguage === 'ar' ? 'تم نسخ الحديث إلى الحافظة' :
              this.currentLanguage === 'fr' ? 'Hadith copié dans le presse-papier' :
              'Hadith copied to clipboard',
        duration: 'short',
        position: 'bottom'
      });

      // Sur mobile, ouvrir le dialogue de partage natif
      if (this.platform.is('capacitor')) {
        await Share.share({
          title: this.currentLanguage === 'ar' ? 'حديث من صحاح' :
                 this.currentLanguage === 'fr' ? 'Hadith de Sihaah' :
                 'Hadith from Sihaah',
          text: hadithText,
          dialogTitle: this.currentLanguage === 'ar' ? 'مشاركة هذا الحديث' :
                       this.currentLanguage === 'fr' ? 'Partager ce hadith' :
                       'Share this hadith'
        });
      }

    } catch (error) {
      console.error('Erreur lors du partage du texte:', error);
      await Toast.show({
        text: this.currentLanguage === 'ar' ? 'تعذر مشاركة النص' :
              this.currentLanguage === 'fr' ? 'Impossible de partager le texte' :
              'Unable to share text',
        duration: 'long',
        position: 'bottom'
      });
    }
  }

  // Ajouter ces méthodes pour la localisation du modal de partage
  getShareModalTitle(): string {
    switch (this.currentLanguage) {
      case 'ar':
        return 'مشاركة هذا الحديث';
      case 'fr':
        return 'Partager ce hadith';
      case 'en':
        return 'Share this hadith';
      default:
        return 'Partager ce hadith';
    }
  }

  getImageOptionTitle(): string {
    switch (this.currentLanguage) {
      case 'ar':
        return 'صورة';
      case 'fr':
        return 'Image';
      case 'en':
        return 'Image';
      default:
        return 'Image';
    }
  }

  getImageOptionDescription(): string {
    switch (this.currentLanguage) {
      case 'ar':
        return 'مشاركة كصورة جميلة';
      case 'fr':
        return 'Partager comme une belle image';
      case 'en':
        return 'Share as a beautiful image';
      default:
        return 'Partager comme une belle image';
    }
  }

  getTextOptionTitle(): string {
    switch (this.currentLanguage) {
      case 'ar':
        return 'نص';
      case 'fr':
        return 'Texte';
      case 'en':
        return 'Text';
      default:
        return 'Texte';
    }
  }

  getTextOptionDescription(): string {
    switch (this.currentLanguage) {
      case 'ar':
        return 'نسخ ومشاركة النص';
      case 'fr':
        return 'Copier et partager le texte';
      case 'en':
        return 'Copy and share the text';
      default:
        return 'Copier et partager le texte';
    }
  }

  updateCanGoBack() {
    console.log('updateCanGoBack called');
    this.canGoBack = this.hadithService.canGoBack();
    console.log('Can go back updated:', this.canGoBack);
  }

  // Charger les hadiths sauvegardés
  async loadSavedHadiths() {
    const { value } = await Preferences.get({ key: 'savedHadiths' });
    if (value) {
      this.savedHadiths = JSON.parse(value);
    }
  }

  // Sauvegarder un hadith
  async saveHadith(event?: Event) {
    if (event) {
      event.stopPropagation();
    }

    if (!this.currentHadith) return;

    try {
      // Afficher l'animation
      this.showBookmarkAnimation = true;

      // Cacher l'animation après 1 seconde
      setTimeout(() => {
        this.zone.run(() => {
          this.showBookmarkAnimation = false;
        });
      }, 1000);

      // Récupérer la liste actuelle des hadiths sauvegardés
      const { value } = await Preferences.get({ key: 'savedHadiths' });
      let savedIds: number[] = [];

      if (value) {
        savedIds = JSON.parse(value);
      }

      // Vérifier si le hadith est déjà sauvegardé
      if (!savedIds.includes(this.currentHadith.id)) {
        // Ajouter à la liste
        savedIds.push(this.currentHadith.id);

        // Mettre à jour la liste locale
        this.savedHadiths = savedIds;

        // Sauvegarder dans les préférences
        await Preferences.set({
          key: 'savedHadiths',
          value: JSON.stringify(savedIds)
        });

        // Afficher un toast
        await Toast.show({
          text: this.currentLanguage === 'ar' ? 'تم حفظ الحديث' :
                this.currentLanguage === 'fr' ? 'Hadith sauvegardé' :
                'Hadith saved',
          duration: 'short',
          position: 'bottom'
        });
      } else {
        // Afficher un toast indiquant que le hadith est déjà sauvegardé
        await Toast.show({
          text: this.currentLanguage === 'ar' ? 'الحديث محفوظ بالفعل' :
                this.currentLanguage === 'fr' ? 'Hadith déjà sauvegardé' :
                'Hadith already saved',
          duration: 'short',
          position: 'bottom'
        });
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du hadith:', error);
      // Afficher un message d'erreur
      await Toast.show({
        text: this.currentLanguage === 'ar' ? 'خطأ في حفظ الحديث' :
              this.currentLanguage === 'fr' ? 'Erreur lors de la sauvegarde' :
              'Error saving hadith',
        duration: 'short',
        position: 'bottom'
      });
    }
    this.closeFab();
  }

  // Ajouter un gestionnaire de double-clic
  handleDoubleTap(event: Event) {
    // Vérifier si l'événement provient du conteneur de matn
    const target = event.target as HTMLElement;
    if (target.closest('#matn-scroll-container') || target.closest('.matn')) {
      // Ne pas traiter le double-clic s'il provient du conteneur de matn
      return;
    }

    event.stopPropagation();
    this.saveHadith();
  }

  // Ajouter une méthode pour naviguer vers la page des hadiths sauvegardés
  goToSavedHadiths(event?: Event) {
    if (event) {
      event.stopPropagation();
    }
    this.closeFab();
    this.router.navigate(['/saved-hadiths']);
  }

  // Méthode pour gérer le clic sur le FAB
  handleFabClick(event: Event) {
    event.stopPropagation();
    this.fabOpen = !this.fabOpen;
  }

  // Méthode pour fermer le FAB
  closeFab() {
    if (this.mainFab) {
      this.fabOpen = false;
      // Fermer le FAB programmatiquement
      this.mainFab.close();
    }
  }

  // Nouvelles méthodes pour gérer les clics avec animation
  handleLeftZoneClick() {
    const isRTL = this.currentLanguage === 'ar';

    // Réinitialiser le scroll immédiatement
    this.resetScrollPosition();

    if (isRTL) {
      // En arabe (RTL), clic gauche = suivant avec animation vers la droite
      this.animateHorizontalTransition('right', () => {
        this.nextHadith();
      });
    } else if (this.canGoBack) {
      // En LTR, clic gauche = précédent (si possible) avec animation vers la droite
      this.animateHorizontalTransition('right', () => {
        this.previousHadith();
      });
    }
  }

  handleRightZoneClick() {
    const isRTL = this.currentLanguage === 'ar';

    // Réinitialiser le scroll immédiatement
    this.resetScrollPosition();

    if (isRTL && this.canGoBack) {
      // En arabe (RTL), clic droit = précédent avec animation vers la gauche
      this.animateHorizontalTransition('left', () => {
        this.previousHadith();
      });
    } else if (!isRTL) {
      // En LTR, clic droit = suivant avec animation vers la gauche
      this.animateHorizontalTransition('left', () => {
        this.nextHadith();
      });
    }
  }

  // Nouvelle méthode pour charger le hadith initial
  async loadInitialHadith() {
    try {
      // Afficher le spinner de chargement
      this.connectionError = false;

      // Attendre plus longtemps pour le chargement initial
      const loadingTimeout = 30000; // 30 secondes

      // Créer une promesse avec timeout
      const loadingPromise = new Promise<void>(async (resolve, reject) => {
        // Vérifier si les hadiths sont déjà chargés
        if (this.hadithService.isHadithsLoaded()) {
          this.checkAndLoadHadith();
          resolve();
        } else {
          // Attendre que les hadiths soient chargés avec un délai plus long
          try {
            await this.hadithService.waitForHadithsLoaded();
            this.checkAndLoadHadith();
            resolve();
          } catch (error) {
            reject(error);
          }
        }
      });

      // Ajouter un timeout
      const timeoutPromise = new Promise<void>((_, reject) => {
        setTimeout(() => reject(new Error('Timeout loading hadiths')), loadingTimeout);
      });

      // Attendre le chargement ou le timeout
      await Promise.race([loadingPromise, timeoutPromise]);

      // Réinitialiser l'état d'erreur si le chargement réussit et qu'un hadith est chargé
      if (this.currentHadith) {
        this.connectionError = false;
      } else {
        // Si aucun hadith n'est chargé, c'est probablement dû à une erreur réseau
        this.connectionError = true;
      }
    } catch (error) {
      console.error('Erreur lors du chargement des hadiths:', error);
      // Afficher le message d'erreur
      this.connectionError = true;
      // Ne pas charger de hadith par défaut
      this.currentHadith = null;
    }
  }

  // Méthode pour vérifier et charger le hadith approprié
  private checkAndLoadHadith() {
    const hadithToViewId = this.hadithService.getHadithToView();
    if (hadithToViewId) {
      console.log('Loading specific hadith:', hadithToViewId);
      this.hadithService.getHadithById(hadithToViewId).then(hadith => {
        if (hadith) {
          this.currentHadith = hadith;
          this.hadithService.addToHistory(hadithToViewId);
          this.updateHadithStats();
          this.updateCanGoBack();

          // Attendre que le DOM soit mis à jour puis réinitialiser le scroll
          setTimeout(() => {
            this.checkMatnOverflow();
            this.resetScrollPosition();
          }, 200);

          console.log('Specific hadith loaded successfully:', hadith.id);
        } else {
          console.log('Specific hadith not found, loading next hadith');
          this.loadNextHadith();
        }
      }).catch(error => {
        console.error('Error loading specific hadith:', error);
        this.loadNextHadith();
      });
    } else {
      // Pas de hadith spécifique à charger, continuer normalement
      if (!this.currentHadith) {
        this.loadNextHadith();
      }
    }
  }

  // Ajouter une méthode pour réessayer le chargement
  async retryLoading() {
    console.log('Retry loading called');

    try {
      // Afficher un toast pour indiquer que nous essayons de recharger
      await Toast.show({
        text: this.currentLanguage === 'ar' ? 'جاري إعادة المحاولة...' :
              this.currentLanguage === 'fr' ? 'Tentative de rechargement...' :
              'Trying to reload...',
        duration: 'short',
        position: 'center'
      });

      // Réinitialiser l'état d'erreur
      this.connectionError = false;

      // Forcer le rechargement des hadiths
      await this.hadithService.loadHadiths();

      // Vérifier si des hadiths ont été chargés
      if (this.hadithService.isHadithsLoaded()) {
        console.log('Hadiths loaded successfully');
        console.log('Hadiths count:', this.hadithService.getViewedHadithsStats().total);

        // Vérifier si le tableau de hadiths est vide
        if (this.hadithService.getViewedHadithsStats().total === 0) {
          console.log('Hadiths array is empty');
          this.connectionError = true;
          throw new Error('Hadiths array is empty');
        }

        // Charger un hadith
        this.checkAndLoadHadith();

        // Attendre un peu pour s'assurer que le hadith est chargé
        await new Promise(resolve => setTimeout(resolve, 100));

        // Vérifier si un hadith a été chargé
        if (this.currentHadith) {
          console.log('Hadith loaded successfully:', this.currentHadith.id);

          // Réinitialiser les gestes après que l'erreur est résolue
          setTimeout(() => {
            this.resetGestures();
          }, 300);

          // Afficher un toast de succès
          await Toast.show({
            text: this.currentLanguage === 'ar' ? 'تم التحميل بنجاح' :
                  this.currentLanguage === 'fr' ? 'Chargement réussi' :
                  'Loading successful',
            duration: 'short',
            position: 'bottom'
          });
        } else {
          console.log('No hadith loaded, forcing load of first hadith');

          // Essayer de charger explicitement le premier hadith disponible
          const stats = this.hadithService.getViewedHadithsStats();
          if (stats.total > 0) {
            // Obtenir le premier hadith disponible
            this.hadithService.getNextHadith().then(firstHadith => {
              if (firstHadith) {
                console.log('First hadith loaded:', firstHadith.id);
                this.currentHadith = firstHadith;
                this.updateHadithStats();
                this.updateCanGoBack();

                // Réinitialiser les gestes
                setTimeout(() => {
                  this.resetGestures();
                  // Réinitialiser la position de scroll
                  this.resetScrollPosition();
                }, 400);

                // Afficher un toast de succès
                Toast.show({
                  text: this.currentLanguage === 'ar' ? 'تم التحميل بنجاح' :
                        this.currentLanguage === 'fr' ? 'Chargement réussi' :
                        'Loading successful',
                  duration: 'short',
                  position: 'bottom'
                });
              } else {
                console.log('Failed to load first hadith');
                this.connectionError = true;
              }
            });
          } else {
            console.log('No hadiths available');
            this.connectionError = true;
          }
        }
      } else {
        console.log('No hadiths loaded');
        this.connectionError = true;
      }
    } catch (error) {
      console.error('Error in retryLoading:', error);
      // Réafficher le message d'erreur
      this.connectionError = true;
      // Ne pas charger de hadith par défaut
      this.currentHadith = null;

      // Afficher un toast d'erreur
      await Toast.show({
        text: this.currentLanguage === 'ar' ? 'فشل التحميل' :
              this.currentLanguage === 'fr' ? 'Échec du chargement' :
              'Loading failed',
        duration: 'short',
        position: 'bottom'
      });
    }
  }

  // Méthode pour configurer les gestes après que l'erreur de connexion est résolue
  setupGesturesAfterErrorResolved() {
    console.log('Setting up gestures after error resolved');
    // Attendre que le DOM soit mis à jour
    setTimeout(() => {
      if (this.swipeContainer) {
        console.log('DOM updated, setting up swipe gesture');
        this.setupHorizontalSwipeGesture();
        this.checkMatnOverflow();
      } else {
        console.log('DOM not yet updated, waiting...');
        // Réessayer après un délai plus long
        setTimeout(() => {
          if (this.swipeContainer) {
            console.log('DOM updated after delay, setting up swipe gesture');
            this.setupHorizontalSwipeGesture();
            this.checkMatnOverflow();
          } else {
            console.log('DOM still not updated, giving up');
          }
        }, 500);
      }
    }, 100);
  }

  // Méthode pour réinitialiser les gestes
  resetGestures() {
    console.log('Resetting gestures');

    // Détruire le geste existant s'il existe
    if (this.swipeGesture) {
      console.log('Destroying existing gesture');
      this.swipeGesture.destroy();
      this.swipeGesture = undefined;
    }

    // Configurer un nouveau geste après un court délai
    setTimeout(() => {
      console.log('Setting up new gesture after reset');
      this.setupHorizontalSwipeGesture();
    }, 100);
  }

  // Méthode pour afficher l'état actuel (pour le débogage)
  logCurrentState() {
    console.log('--- Current State ---');
    console.log('Current hadith:', this.currentHadith ? this.currentHadith.id : 'null');
    console.log('Can go back:', this.canGoBack);
    console.log('Hadith stats:', this.hadithStats);
    console.log('Connection error:', this.connectionError);
    console.log('--------------------');
  }

  hideSwipeHint() {
    if (this.showSwipeHint) {
      this.showSwipeHint = false;

      // Marquer que l'utilisateur a vu l'indice (après le premier swipe)
      Preferences.set({ key: 'hasSeenSwipeHint', value: 'true' });

      if (this.swipeHintTimer) {
        clearTimeout(this.swipeHintTimer);
      }
    }
  }
}
