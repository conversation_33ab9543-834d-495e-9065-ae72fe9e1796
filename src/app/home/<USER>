/* Custom animations and styles that can't be done with Tailwind */
@keyframes pulse {
  0% {
    transform: translateY(-50%) scale(0.95);
    box-shadow: 0 0 0 0 rgba(var(--ion-color-primary-rgb), 0.5);
  }

  70% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 0 10px rgba(var(--ion-color-primary-rgb), 0);
  }

  100% {
    transform: translateY(-50%) scale(0.95);
    box-shadow: 0 0 0 0 rgba(var(--ion-color-primary-rgb), 0);
  }
}

/* Remove scrollbar */
::-webkit-scrollbar {
  display: none;
}

ion-content {
  --background: transparent;
  --overflow: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* RTL support for Arabic */
:host-context([dir="rtl"]) {
  .sanad, .matn, .rawi {
    direction: rtl;
    text-align: right;
  }
}

/* LTR support for French and English */
:host-context([dir="ltr"]) {
  .sanad, .matn, .rawi {
    direction: ltr;
    text-align: left;
  }
}

/* Safe area handling */
.safe-area-bottom {
  padding-bottom: var(--ion-safe-area-bottom, 1rem);
  margin-bottom: var(--ion-safe-area-bottom, 0);
}

/* Make header transparent */
ion-header {
  &.ion-no-border {
    --border-width: 0;
  }

  ion-toolbar {
    --background: transparent;
    --border-color: transparent;

    &::part(background) {
      background: var(--ion-background-color, #FDF5E8);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }
  }
}

/* Ensure content doesn't overlap with system UI */
.hadith-container {
  // padding-top: calc(var(--ion-safe-area-top, 0px) + 1rem);
  padding-bottom: env(safe-area-inset-bottom);
}

.header-button {
  --padding-start: 0.5rem;
  --padding-end: 0.5rem;
  --padding-top: 0.5rem;
  --padding-bottom: 0.5rem;
  --border-radius: 50%;
  --color: var(--ion-text-color);
  height: 2.5rem;
  width: 2.5rem;
  margin: 0 0.25rem;
  position: relative;
  z-index: 10; /* Augmenter le z-index pour s'assurer que le bouton est au-dessus */
  touch-action: manipulation; /* Optimiser pour les interactions tactiles */

  ion-icon {
    font-size: 1.25rem;
  }

  &::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    z-index: -1;
  }
}

/* Move controls from bottom to header */
.controls {
  display: none; /* Hide the bottom controls since we moved them to header */
}

/* Swipe instructions */
.text-center.mb-4 {
  font-size: 0.875rem;
  color: var(--ion-text-color);
  opacity: 0.7;
  padding: 0.5rem;

  p {
    margin: 0.25rem 0;
  }
}

.control-button {
  --padding-start: 1rem;
  --padding-end: 1rem;
  --padding-top: 0.75rem;
  --padding-bottom: 0.75rem;
  --border-radius: 50%;
  --background: var(--control-background, rgba(255, 255, 255, 0.2));
  --color: var(--ion-text-color);
  height: 3.5rem;
  width: 3.5rem;
  margin: 0 0.5rem;

  ion-icon {
    font-size: 1.5rem;
  }

  &::part(native) {
    box-shadow: var(--control-shadow, 0 4px 12px rgba(0, 0, 0, 0.15));
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.2s ease;
  }

  &:active::part(native) {
    transform: scale(0.95);
  }
}

/* Gestion du débordement pour les grands hadiths */
.hadith-container {
  max-height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  pointer-events: auto !important;

  /* Masquer la scrollbar pour Chrome, Safari et Opera */
  &::-webkit-scrollbar {
    display: none;
  }
}

/* Structure de base */
.swipe-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow-x: hidden;
  pointer-events: auto !important;
}

/* Contenu du hadith */
.hadith-content {
  width: 100%;
  max-width: 90%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  z-index: 20; /* Plus élevé que les zones de clic */
}

/* Sanad (maintenant title) toujours visible en haut et CENTRÉ */
.sanad {
  width: 100%;
  text-align: center !important;
  word-wrap: break-word;
  padding-bottom: 1rem;
  flex-shrink: 0;
}

/* Conteneur pour le matn avec défilement vertical */
.matn-container {
  flex: 1;
  width: 100%;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  -webkit-overflow-scrolling: touch !important;
  margin: 1rem 0;
  position: relative;
  z-index: 50;
  touch-action: auto !important;
  pointer-events: auto !important;
}

/* Matn avec alignement selon la langue */
.matn {
  width: 100%;
  padding: 0.5rem 0;
  touch-action: auto !important;
  pointer-events: auto !important;

  /* Alignement du texte selon la langue */
  &[lang="ar"] {
    text-align: right;
    direction: rtl;
  }

  &[lang="fr"], &[lang="en"] {
    text-align: left;
    direction: ltr;
  }
}

/* Rawi (maintenant attribution) toujours visible en bas et CENTRÉ */
.rawi {
  width: 100%;
  text-align: center !important;
  word-wrap: break-word;
  padding-top: 1rem;
  flex-shrink: 0;
  opacity: 0.7;
}

/* Indicateur de swipe horizontal */
.swipe-indicator {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 1.5rem;
  pointer-events: none; /* Ne pas interférer avec les interactions */
  opacity: 0.5;
  z-index: 10;
}

.swipe-arrow {
  font-size: 1.5rem;
  color: var(--ion-text-color);
}

/* Indicateur de progression */
.progress-indicator {
  position: absolute;
  bottom: 1rem; /* Plus d'espace en bas */
  left: 0;
  right: 0;
  text-align: center;
  font-size: 0.75rem;
  opacity: 0.5;
  z-index: 10;
}

/* Styles spécifiques pour l'arabe */
[lang="ar"] {
  .matn {
    text-align: right;
    direction: rtl;
    font-family: 'Amiri', serif;
    font-size: 2rem;
    line-height: 1.8;
  }

  .sanad {
    font-family: 'Amiri', serif;
    font-size: 1.25rem;
    line-height: 1.6;
    text-align: center !important;
  }

  .rawi {
    font-family: 'Amiri', serif;
    font-size: 1.125rem;
    line-height: 1.6;
    text-align: center !important;
  }
}

/* Styles pour les autres langues */
[lang="fr"], [lang="en"] {
  .matn {
    text-align: left;
    direction: ltr;
    font-size: 1.5rem;
    line-height: 1.6;
  }

  .sanad {
    font-size: 1.125rem;
    line-height: 1.5;
    opacity: 0.8;
    text-align: center !important;
  }

  .rawi {
    font-size: 1rem;
    line-height: 1.5;
    text-align: center !important;
  }
}

/* Ajout d'espace supplémentaire pour les appareils avec notch/safe areas */
@supports (padding: max(0px)) {
  .swipe-container {
    padding-top: max(1rem, env(safe-area-inset-top));
    padding-bottom: max(2rem, env(safe-area-inset-bottom));
  }

  .progress-indicator {
    bottom: max(1rem, env(safe-area-inset-bottom));
  }
}

/* Zones de clic pour navigation - étendues à toute la zone hors matn */
.click-zone {
  position: absolute;
  top: 0;
  height: 100%;
  z-index: 5;

  &.click-zone-left {
    left: 0;
    width: 40%; /* Étendu à 40% de la largeur de l'écran */
  }

  &.click-zone-right {
    right: 0;
    width: 40%; /* Étendu à 40% de la largeur de l'écran */
  }
}

/* Assurer que le matn-container est au-dessus des zones de clic */
#matn-scroll-container {
  position: relative;
  z-index: 10;
  width: 80%; /* Limiter la largeur pour laisser de l'espace aux zones de clic */
  margin: 0 auto; /* Centrer horizontalement */
  pointer-events: auto !important; /* S'assurer que les événements sont capturés */
}

/* Zone de double-clic au centre (50% du milieu) */
.double-tap-zone {
  position: absolute;
  top: 0;
  left: 25%; /* Commence après la zone de clic gauche */
  width: 50%; /* Occupe le milieu */
  height: 100%;
  z-index: 4; /* En dessous des boutons mais au-dessus du fond */
  pointer-events: auto;
}

/* Assurer que les zones de clic n'interfèrent pas avec le défilement */
.click-zone, .double-tap-zone {
  pointer-events: none;
  z-index: 5;

  /* Réactiver les événements uniquement quand on est en dehors du matn-container */
  &:not(.matn-container):not(.matn) {
    pointer-events: auto;
  }
}

/* Animation de l'icône bookmark */
.bookmark-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  pointer-events: none;
  animation: bookmark-pulse 1s ease-out;

  ion-icon {
    font-size: 100px;
    opacity: 0.9;
    filter: drop-shadow(0 0 10px rgba(0, 0, 0, 0.3));
  }
}

/* Couleurs spécifiques selon le thème */
:host-context(body:not(.dark)) .bookmark-animation ion-icon {
  color: rgba(0, 60, 68, 0.8); /* Vert transparent pour le mode clair */
}

:host-context(body.dark) .bookmark-animation ion-icon {
  color: rgba(255, 255, 255, 0.8); /* Blanc transparent pour le mode sombre */
}

@keyframes bookmark-pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

/* S'assurer que le contenu du matn est scrollable */
.matn {
  width: 100%;
  padding: 0.5rem 0;
  pointer-events: auto; /* Assurer que les événements de scroll sont capturés */

  /* Alignement du texte selon la langue */
  &[lang="ar"] {
    text-align: right;
    direction: rtl;
  }

  &[lang="fr"], &[lang="en"] {
    text-align: left;
    direction: ltr;
  }
}

/* S'assurer que le swipe-container n'interfère pas avec le scroll */
.swipe-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow-x: hidden;
  pointer-events: auto; /* Permettre les événements de scroll */
}

/* Mais permettre les événements sur les éléments enfants */
.hadith-content, .click-zone, .matn-container, .matn {
  pointer-events: auto !important;
  touch-action: auto !important;
}

/* Styles pour le bouton de partage */
.header-button {
  --padding-start: 0.5rem;
  --padding-end: 0.5rem;
  --padding-top: 0.5rem;
  --padding-bottom: 0.5rem;
  --border-radius: 50%;
  --color: var(--ion-text-color);
  height: 2.5rem;
  width: 2.5rem;
  margin: 0 0.25rem;
  position: relative;
  z-index: 10;
  touch-action: manipulation;

  ion-icon {
    font-size: 1.25rem;
  }
}

/* Modal de partage - Style amélioré avec animation du bas */
.share-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-end; /* Aligner en bas */
  z-index: 1000;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  animation: overlayFadeIn 0.3s ease-out;
}

@keyframes overlayFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.share-modal {
  width: 100%;
  max-width: 500px;
  background-color: var(--ion-background-color);
  border-radius: 16px 16px 0 0; /* Arrondir uniquement les coins supérieurs */
  box-shadow: 0 -4px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: modalSlideUp 0.3s ease-out;
  direction: ltr !important;
  text-align: left !important;
  transform-origin: bottom center;
}

@keyframes modalSlideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

.share-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.share-modal-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-button {
  --padding-start: 8px;
  --padding-end: 8px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  margin: 0;
  height: 36px;
}

.share-options {
  padding: 16px;
}

.share-option {
  display: flex;
  align-items: center;
  padding: 16px;
  margin-bottom: 8px;
  border-radius: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  direction: ltr !important;
  text-align: left !important;
}

.share-option:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.share-option:active {
  background-color: rgba(0, 0, 0, 0.1);
}

.share-option-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 56px;
  height: 56px;
  border-radius: 16px;
  margin-right: 20px;
}

/* Styles pour les icônes du modal de partage */
.share-option-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 56px;
  height: 56px;
  border-radius: 16px;
  margin-right: 20px;
}

/* Mode clair - couleur unie légèrement plus foncée que le fond */
.share-option-icon {
  background-color: #f0e8d8; /* Version plus foncée du beige de fond */
  color: #003C44;
}

.share-option-icon ion-icon {
  font-size: 28px;
  color: #003C44;
}

/* Mode sombre - couleur unie légèrement plus claire que le fond */
:host-context(.dark-1) .share-option-icon,
:host-context(.dark-2) .share-option-icon,
:host-context(body.dark) .share-option-icon {
  background-color: #004a55; /* Version plus claire du vert de fond */
  color: #FDF5E8;
}

:host-context(.dark-1) .share-option-icon ion-icon,
:host-context(.dark-2) .share-option-icon ion-icon,
:host-context(body.dark) .share-option-icon ion-icon {
  color: #FDF5E8;
}

.share-option-text {
  flex: 1;
}

.share-option-text h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.share-option-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.7;
}

/* Conteneur pour la génération d'image */
.image-container {
  position: absolute;
  left: -9999px;
  top: -9999px;
  width: 1080px; /* Largeur fixe pour l'image */
  background-color: var(--ion-background-color);
  overflow: hidden;
  padding: 40px;
  padding-bottom: 60px; /* Augmenter le padding en bas */
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  /* Pas de hauteur fixe pour s'adapter au contenu */

  &.dark-theme {
    background-color: #003C44; /* Couleur verte de l'app */
    color: #ffffff;
  }
}

.hadith-image-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.hadith-image-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.hadith-image-logo-container {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24px;
}

.hadith-image-logo {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.hadith-image-app-name {
  font-size: 32px;
  font-weight: 600;
  margin: 0;
  display: inline-block;
  line-height: 60px; /* Exactement la même hauteur que le logo */
}

/* Styles RTL pour l'arabe */
.image-container[lang="ar"] {
  direction: rtl;

  .hadith-image-header {
    direction: ltr; /* Forcer LTR pour l'en-tête même en arabe */
  }

  .hadith-image-matn {
    text-align: right;
    direction: rtl;
  }
}

/* Styles LTR pour le français et l'anglais */
.image-container[lang="fr"],
.image-container[lang="en"] {
  direction: ltr;

  .hadith-image-logo-container {
    margin-right: 24px;
    margin-left: 0;
  }

  .hadith-image-matn {
    text-align: left;
    direction: ltr;
  }
}

.hadith-image-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Distribuer l'espace verticalement */
  padding: 40px 0;
  min-height: 500px; /* Hauteur minimale pour assurer l'espace */
}

.hadith-image-sanad {
  font-size: 22px;
  margin-bottom: 30px;
  text-align: center !important; /* Forcer le centrage */
  width: 100%;

  [lang="ar"] & {
    font-family: 'Amiri', serif;
    font-size: 26px;
  }
}

.hadith-image-matn {
  font-size: 28px;
  line-height: 1.6;
  margin: auto 0; /* Centrer verticalement */
  flex: 1;
  display: flex;
  align-items: center; /* Centrer le contenu verticalement */

  [lang="ar"] & {
    font-family: 'Amiri', serif;
    font-size: 36px;
    line-height: 1.8;
  }
}

.hadith-image-rawi {
  font-size: 20px;
  opacity: 0.8;
  text-align: center !important; /* Forcer le centrage */
  width: 100%;
  margin-top: 30px;

  [lang="ar"] & {
    font-family: 'Amiri', serif;
    font-size: 24px;
  }
}

.hadith-image-footer {
  display: flex;
  justify-content: center;
  padding-top: 30px;
  font-size: 18px;
  opacity: 0.6;
  margin-bottom: 20px; /* Ajouter une marge en bas */
}

/* Ajustement spécifique pour l'arabe */
.image-container[lang="ar"] {
  .hadith-image-header {
    flex-direction: row-reverse; /* Inverser l'ordre en arabe */
  }

  .hadith-image-logo-container {
    margin-right: 0;
    margin-left: 24px; /* Espace à gauche en arabe */
  }

  .hadith-image-app-name {
    margin-right: 0;
  }
}

/* Styles LTR pour le français et l'anglais */
.image-container[lang="fr"] .hadith-image-logo-container,
.image-container[lang="en"] .hadith-image-logo-container {
  margin-right: 24px;
  margin-left: 0;
}

/* Styles pour les boutons flottants */
.save-button-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 100;
}

.floating-button {
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 0;
  --padding-bottom: 0;
  --border-radius: 50%;
  width: 56px;
  height: 56px;
  margin: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.save-button {
  --background: var(--ion-color-light);
  --color: var(--ion-color-primary);
}

/* Styles pour les icônes des boutons flottants */
.floating-button ion-icon {
  font-size: 24px;
}

/* Ajustement pour le mode sombre */
:host-context(body.dark) .save-button {
  --background: var(--ion-color-dark);
  --color: var(--ion-color-light);
}

/* Styles pour le FAB - semi-transparent */
ion-fab[horizontal="end"] {
  right: 20px !important;
  left: auto !important;
}

/* S'assurer que le FAB reste à droite en mode RTL */
:host-context([dir="rtl"]) ion-fab[horizontal="end"] {
  right: 20px !important;
  left: auto !important;
}

/* Bouton principal du FAB avec transparence */
ion-fab-button {
  --background: rgba(0, 60, 68, 0.8); /* Couleur verte par défaut (pour le mode sombre) */
  --color: white;
}

/* Mode clair - couleur beige pour le bouton principal */
:host-context(body:not(.dark)) ion-fab-button {
  --background: rgba(253, 245, 232, 0.8); /* Même couleur que le fond (#FDF5E8) avec 80% d'opacité */
  --color: var(--ion-color-primary); /* Couleur du texte verte */
}

/* Boutons du menu FAB avec transparence */
ion-fab-list ion-fab-button {
  --background: rgba(253, 245, 232, 0.8); /* Couleur beige avec 80% d'opacité */
  --color: var(--ion-color-primary);
}

/* Ajustements pour le mode sombre */
:host-context(body.dark) ion-fab-list ion-fab-button {
  --background: rgba(30, 30, 30, 0.8); /* Couleur sombre avec 80% d'opacité */
  --color: white;
}

/* Styles pour le message d'erreur de connexion */
.connection-error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  text-align: center;
}

.connection-error-icon {
  font-size: 5rem;
  color: var(--ion-color-medium);
  margin-bottom: 1rem;
}

.connection-error-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: var(--ion-color-dark);
}

.connection-error-message {
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 2rem;
  color: var(--ion-color-medium-shade);
}

.retry-button {
  --background: var(--ion-color-primary);
  --color: white;
  --border-radius: 8px;
  --padding-start: 2rem;
  --padding-end: 2rem;
}

/* Ajustement pour le mode sombre */
:host-context(.dark) {
  .connection-error-title {
    color: var(--ion-color-light);
  }

  .connection-error-message {
    color: var(--ion-color-medium-tint);
  }
}

/* Centrer verticalement le matn quand il n'est pas scrollable */
.matn-container:not(.has-overflow) {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Styles pour l'indice de swipe */
.swipe-hint {
  position: absolute;
  bottom: 20%;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  pointer-events: none; /* Ne pas interférer avec les interactions */
}

.swipe-hint-content {
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 20px;
  padding: 8px 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.swipe-text {
  color: white;
  font-size: 14px;
  margin: 0 10px;
}

.swipe-arrow {
  animation: pulse-arrow 1.5s infinite ease-in-out;
  color: white;

  &.left {
    animation-delay: 0s;
  }

  &.right {
    animation-delay: 0.75s;
  }

  ion-icon {
    font-size: 20px;
  }
}

@keyframes pulse-arrow {
  0% {
    opacity: 0.4;
    transform: translateX(0);
  }
  50% {
    opacity: 1;
    transform: translateX(3px);
  }
  100% {
    opacity: 0.4;
    transform: translateX(0);
  }
}

/* Inverser l'animation pour la flèche gauche */
.swipe-arrow.left {
  @keyframes pulse-arrow {
    0% {
      opacity: 0.4;
      transform: translateX(0);
    }
    50% {
      opacity: 1;
      transform: translateX(-3px);
    }
    100% {
      opacity: 0.4;
      transform: translateX(0);
    }
  }
}

/* Ajustement RTL pour l'arabe */
:host-context([dir="rtl"]) {
  .swipe-arrow.left {
    transform: scaleX(-1);
  }

  .swipe-arrow.right {
    transform: scaleX(-1);
  }
}

/* Styles pour le spinner de chargement */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  margin-bottom: 1rem;
  color: var(--ion-color-primary);
}

.loading-text {
  font-size: 1.2rem;
  color: var(--ion-color-medium);
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}
