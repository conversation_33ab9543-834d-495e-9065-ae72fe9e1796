import { Component, Input, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonIcon } from '@ionic/angular/standalone';
import { ThemeService } from '../../services/theme.service';

@Component({
  selector: 'app-hadith-image',
  templateUrl: './hadith-image.component.html',
  styleUrls: ['./hadith-image.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class HadithImageComponent {
  @Input() currentHadith: any;
  @Input() currentLanguage: string = 'ar';
  @ViewChild('imageContainer', { static: true }) imageContainer!: ElementRef;

  constructor(public themeService: ThemeService) {}

  // Méthode pour accéder au conteneur d'image depuis le composant parent
  getImageContainer(): ElementRef {
    return this.imageContainer;
  }
}
