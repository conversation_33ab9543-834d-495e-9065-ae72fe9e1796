/* Conteneur pour la génération d'image */
.image-container {
  position: absolute;
  left: -9999px;
  top: -9999px;
  width: 1080px; /* Largeur fixe pour l'image */
  background-color: var(--ion-background-color);
  overflow: hidden;
  padding: 40px;
  padding-bottom: 60px; /* Augmenter le padding en bas */
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  /* Pas de hauteur fixe pour s'adapter au contenu */

  &.dark-theme {
    background-color: #003C44; /* Couleur verte de l'app */
    color: #ffffff;
  }
}

.hadith-image-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.hadith-image-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.hadith-image-logo-container {
  margin-right: 24px;
  margin-left: 0;
}

.hadith-image-logo {
  width: 40px;
  height: 40px;
}

.hadith-image-app-name {
  font-size: 24px;
  font-weight: 600;

  [lang="ar"] & {
    font-family: 'Amiri', serif;
    font-size: 28px; /* Légèrement plus grand pour l'arabe */
  }
}

/* Styles LTR pour le français et l'anglais */
.image-container[lang="fr"],
.image-container[lang="en"] {
  direction: ltr;

  .hadith-image-logo-container {
    margin-right: 0;
    margin-left: 0;
  }

  .hadith-image-matn {
    text-align: left;
    direction: ltr;
  }
}

/* Styles spécifiques pour l'arabe */
.image-container[lang="ar"] {
  .hadith-image-app-name {
    font-family: 'Amiri', serif;
    font-size: 28px;
  }
}

.hadith-image-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Distribuer l'espace verticalement */
  padding: 40px 0;
  min-height: 500px; /* Hauteur minimale pour assurer l'espace */
}

.hadith-image-sanad {
  font-size: 22px;
  margin-bottom: 30px;
  text-align: center !important; /* Forcer le centrage */
  width: 100%;

  [lang="ar"] & {
    font-family: 'Amiri', serif;
    font-size: 26px;
  }
}

.hadith-image-matn {
  font-size: 28px;
  line-height: 1.6;
  margin: auto 0; /* Centrer verticalement */
  flex: 1;
  display: flex;
  align-items: center; /* Centrer le contenu verticalement */

  [lang="ar"] & {
    font-family: 'Amiri', serif;
    font-size: 36px;
    line-height: 1.8;
  }
}

.hadith-image-rawi {
  font-size: 20px;
  opacity: 0.8;
  text-align: center !important; /* Forcer le centrage */
  width: 100%;
  margin-top: 30px;

  [lang="ar"] & {
    font-family: 'Amiri', serif;
    font-size: 24px;
  }
}

.hadith-image-footer {
  display: flex;
  justify-content: center;
  padding-top: 30px;
  font-size: 18px;
  opacity: 0.6;
  margin-bottom: 20px; /* Ajouter une marge en bas */
}
