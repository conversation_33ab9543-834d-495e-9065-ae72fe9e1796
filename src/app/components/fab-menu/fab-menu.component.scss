/* Styles pour le FAB - semi-transparent */
ion-fab[horizontal="end"] {
  right: 20px !important;
  left: auto !important;
}

/* S'assurer que le FAB reste à droite en mode RTL */
:host-context([dir="rtl"]) ion-fab[horizontal="end"] {
  right: 20px !important;
  left: auto !important;
}

/* Bouton principal du FAB avec transparence et effet de verre */
ion-fab-button {
  --background: rgba(0, 60, 68, 0.85); /* Couleur verte par défaut (pour le mode sombre) */
  --color: white;
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  --border-radius: 16px;
  transition: all 0.3s ease;

  &::part(native) {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  &:hover {
    --background: rgba(0, 60, 68, 0.95);
    --box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25);
    transform: translateY(-2px);
  }
}

/* Mode clair - couleur beige pour le bouton principal */
:host-context(body:not(.dark)) ion-fab-button {
  --background: rgba(253, 245, 232, 0.85); /* Même couleur que le fond (#FDF5E8) avec 85% d'opacité */
  --color: var(--ion-color-primary); /* Couleur du texte verte */
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  &:hover {
    --background: rgba(253, 245, 232, 0.95);
    --box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }
}

/* Correction pour la transition d'ouverture/fermeture en mode clair */
:host-context(body:not(.dark)) ion-fab-button::part(native) {
  background: rgba(253, 245, 232, 0.85) !important;
  transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

:host-context(body:not(.dark)) ion-fab[activated="true"] ion-fab-button::part(native),
:host-context(body:not(.dark)) ion-fab-button.activated::part(native) {
  background: rgba(253, 245, 232, 0.95) !important;
}

/* Correction pour l'animation d'ouverture/fermeture en mode clair */
:host-context(body:not(.dark)) ion-fab-button.ion-activated::part(native) {
  background: rgba(253, 245, 232, 0.95) !important;
}

/* Correction pour le focus en mode clair */
:host-context(body:not(.dark)) ion-fab-button:focus::part(native) {
  background: rgba(253, 245, 232, 0.95) !important;
}

/* Boutons du menu FAB avec transparence et effet de verre */
ion-fab-list ion-fab-button {
  --background: rgba(253, 245, 232, 0.85); /* Couleur beige avec 85% d'opacité */
  --color: var(--ion-color-primary);
  --box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  --border-radius: 14px;
  margin: 8px 0;

  &::part(native) {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  &:hover {
    --background: rgba(253, 245, 232, 0.95);
    transform: scale(1.05);
  }
}

/* Ajustements pour le mode sombre */
:host-context(body.dark) ion-fab-list ion-fab-button {
  --background: rgba(0, 30, 34, 0.85); /* Couleur sombre avec 85% d'opacité */
  --color: white;
  --box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);

  &:hover {
    --background: rgba(0, 30, 34, 0.95);
  }
}

/* Animation d'ouverture du FAB */
ion-fab-list.fab-list-active ion-fab-button {
  animation: fadeInScale 0.2s ease-out forwards;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Cacher le FAB */
.hidden {
  display: none !important;
}
