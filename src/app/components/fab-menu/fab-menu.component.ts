import { Component, Input, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonFab, IonFabButton, IonFabList, IonIcon } from '@ionic/angular/standalone';

@Component({
  selector: 'app-fab-menu',
  templateUrl: './fab-menu.component.html',
  styleUrls: ['./fab-menu.component.scss'],
  standalone: true,
  imports: [CommonModule, IonFab, IonFabButton, IonFabList, IonIcon]
})
export class FabMenuComponent {
  @Input() hidden: boolean = false;
  @ViewChild('mainFab', { static: false }) mainFab: any;
  fabOpen = false;
  
  handleFabClick(event: Event) {
    event.stopPropagation();
    this.fabOpen = !this.fabOpen;
  }
  
  saveHadith(event: Event) {
    event.stopPropagation();
    this.closeFab();
    // Émettre un événement pour le composant parent
    const customEvent = new CustomEvent('saveHadith', { detail: event });
    window.dispatchEvent(customEvent);
  }
  
  goToSavedHadiths(event: Event) {
    event.stopPropagation();
    this.closeFab();
    // Émettre un événement pour le composant parent
    const customEvent = new CustomEvent('goToSavedHadiths', { detail: event });
    window.dispatchEvent(customEvent);
  }
  
  toggleLanguage(event: Event) {
    event.stopPropagation();
    this.closeFab();
    // Émettre un événement pour le composant parent
    const customEvent = new CustomEvent('toggleLanguage', { detail: event });
    window.dispatchEvent(customEvent);
  }
  
  closeFab() {
    if (this.mainFab) {
      this.fabOpen = false;
      // Fermer le FAB programmatiquement
      this.mainFab.close();
    }
  }
}