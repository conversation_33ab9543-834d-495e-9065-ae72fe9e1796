import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonButton, IonIcon } from '@ionic/angular/standalone';

@Component({
  selector: 'app-connection-error',
  templateUrl: './connection-error.component.html',
  styleUrls: ['./connection-error.component.scss'],
  standalone: true,
  imports: [CommonModule, IonButton, IonIcon]
})
export class ConnectionErrorComponent {
  @Input() show: boolean = false;
  @Input() currentLanguage: string = 'fr';
  @Output() retry = new EventEmitter<void>();
  
  onRetry() {
    this.retry.emit();
  }
  
  getErrorTitle(): string {
    switch (this.currentLanguage) {
      case 'ar':
        return 'لا يوجد اتصال بالإنترنت';
      case 'fr':
        return 'Pas de connexion Internet';
      case 'en':
        return 'No Internet Connection';
      default:
        return 'Pas de connexion Internet';
    }
  }
  
  getErrorMessage(): string {
    switch (this.currentLanguage) {
      case 'ar':
        return 'تحتاج إلى الاتصال بالإنترنت مرة واحدة لتحميل الأحاديث. بعد ذلك، يمكنك استخدام التطبيق بدون إنترنت.';
      case 'fr':
        return 'Une connexion Internet est nécessaire au premier démarrage pour charger les hadiths. Ensuite, vous pourrez utiliser l\'application sans connexion.';
      case 'en':
        return 'An internet connection is required on first launch to load the hadiths. Afterwards, you can use the app offline.';
      default:
        return 'Une connexion Internet est nécessaire au premier démarrage pour charger les hadiths. Ensuite, vous pourrez utiliser l\'application sans connexion.';
    }
  }
  
  getRetryButtonText(): string {
    switch (this.currentLanguage) {
      case 'ar':
        return 'إعادة المحاولة';
      case 'fr':
        return 'Réessayer';
      case 'en':
        return 'Retry';
      default:
        return 'Réessayer';
    }
  }
}