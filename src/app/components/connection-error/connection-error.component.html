<div *ngIf="show" class="connection-error-container">
  <div class="connection-error-content">
    <ion-icon name="cloud-offline-outline" class="connection-error-icon"></ion-icon>
    <h2 class="connection-error-title">
      {{ getErrorTitle() }}
    </h2>
    <p class="connection-error-message">
      {{ getErrorMessage() }}
    </p>
    <ion-button (click)="onRetry()" class="retry-button">
      {{ getRetryButtonText() }}
    </ion-button>
  </div>
</div>