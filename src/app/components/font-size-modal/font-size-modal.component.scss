.font-size-container {
  padding: 16px;
}

ion-item {
  --padding-start: 16px;
  --padding-end: 16px;
  --border-radius: 12px;
  margin-bottom: 8px;
  
  &.selected {
    --background: rgba(var(--ion-color-primary-rgb), 0.1);
    --color: var(--ion-color-primary);
  }
}

.font-size-small {
  font-size: 14px;
}

.font-size-medium {
  font-size: 16px;
}

.font-size-large {
  font-size: 18px;
}

.font-size-x-large {
  font-size: 20px;
}

ion-toolbar {
  --background: transparent;
}

ion-header {
  background: transparent;
}

ion-content {
  --background: var(--ion-background-color);
}