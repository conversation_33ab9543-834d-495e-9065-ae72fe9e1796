<ion-header class="ion-no-border">
  <ion-toolbar>
    <ion-title><PERSON><PERSON> du texte</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="close()">
        <ion-icon name="close-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="font-size-container">
    <ion-list lines="none">
      <ion-item button (click)="selectFontSize('small')" [class.selected]="currentFontSize === 'small'">
        <ion-label class="font-size-small">Petite</ion-label>
        <ion-icon *ngIf="currentFontSize === 'small'" name="checkmark" slot="end"></ion-icon>
      </ion-item>

      <ion-item button (click)="selectFontSize('medium')" [class.selected]="currentFontSize === 'medium'">
        <ion-label class="font-size-medium">Moyenne</ion-label>
        <ion-icon *ngIf="currentFontSize === 'medium'" name="checkmark" slot="end"></ion-icon>
      </ion-item>

      <ion-item button (click)="selectFontSize('large')" [class.selected]="currentFontSize === 'large'">
        <ion-label class="font-size-large">Grande</ion-label>
        <ion-icon *ngIf="currentFontSize === 'large'" name="checkmark" slot="end"></ion-icon>
      </ion-item>

      <ion-item button (click)="selectFontSize('x-large')" [class.selected]="currentFontSize === 'x-large'">
        <ion-label class="font-size-x-large">Très grande</ion-label>
        <ion-icon *ngIf="currentFontSize === 'x-large'" name="checkmark" slot="end"></ion-icon>
      </ion-item>
    </ion-list>
  </div>
</ion-content>
