import { Component, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { FontSize, FontSizeService } from 'src/app/services/font-size.service';

@Component({
  selector: 'app-font-size-modal',
  templateUrl: './font-size-modal.component.html',
  styleUrls: ['./font-size-modal.component.scss'],
})
export class FontSizeModalComponent implements OnInit {
  currentFontSize: FontSize;
  
  constructor(
    private modalCtrl: ModalController,
    private fontSizeService: FontSizeService
  ) {}

  ngOnInit() {
    this.currentFontSize = this.fontSizeService.getCurrentFontSize();
  }

  selectFontSize(size: FontSize) {
    this.currentFontSize = size;
    this.fontSizeService.setFontSize(size);
    this.modalCtrl.dismiss();
  }

  close() {
    this.modalCtrl.dismiss();
  }
}