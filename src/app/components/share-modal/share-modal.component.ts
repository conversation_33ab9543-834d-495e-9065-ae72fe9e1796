import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonButton, IonIcon } from '@ionic/angular/standalone';

@Component({
  selector: 'app-share-modal',
  templateUrl: './share-modal.component.html',
  styleUrls: ['./share-modal.component.scss'],
  standalone: true,
  imports: [CommonModule, IonButton, IonIcon]
})
export class ShareModalComponent {
  @Input() show: boolean = false;
  @Input() currentLanguage: string = 'fr';
  @Output() close = new EventEmitter<Event>();
  @Output() shareImage = new EventEmitter<void>();
  @Output() shareText = new EventEmitter<void>();
  
  closeModal(event: Event) {
    this.close.emit(event);
  }
  
  onShareAsImage() {
    this.shareImage.emit();
  }
  
  onShareAsText() {
    this.shareText.emit();
  }
  
  getShareModalTitle(): string {
    switch (this.currentLanguage) {
      case 'ar':
        return 'مشاركة هذا الحديث';
      case 'fr':
        return 'Partager ce hadith';
      case 'en':
        return 'Share this hadith';
      default:
        return 'Partager ce hadith';
    }
  }

  getImageOptionTitle(): string {
    switch (this.currentLanguage) {
      case 'ar':
        return 'صورة';
      case 'fr':
        return 'Image';
      case 'en':
        return 'Image';
      default:
        return 'Image';
    }
  }

  getImageOptionDescription(): string {
    switch (this.currentLanguage) {
      case 'ar':
        return 'مشاركة الحديث كصورة';
      case 'fr':
        return 'Partager le hadith comme image';
      case 'en':
        return 'Share the hadith as an image';
      default:
        return 'Partager le hadith comme image';
    }
  }

  getTextOptionTitle(): string {
    switch (this.currentLanguage) {
      case 'ar':
        return 'نص';
      case 'fr':
        return 'Texte';
      case 'en':
        return 'Text';
      default:
        return 'Texte';
    }
  }

  getTextOptionDescription(): string {
    switch (this.currentLanguage) {
      case 'ar':
        return 'نسخ الحديث كنص';
      case 'fr':
        return 'Copier le hadith comme texte';
      case 'en':
        return 'Copy the hadith as text';
      default:
        return 'Copier le hadith comme texte';
    }
  }
}