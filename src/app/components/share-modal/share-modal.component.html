<div *ngIf="show" class="share-modal-overlay" (click)="closeModal($event)">
  <div class="share-modal" (click)="$event.stopPropagation()">
    <div class="share-modal-header">
      <h2>{{ getShareModalTitle() }}</h2>
      <ion-button fill="clear" (click)="closeModal($event)" class="close-button">
        <ion-icon name="close-outline"></ion-icon>
      </ion-button>
    </div>
    <div class="share-options">
      <div class="share-option" (click)="onShareAsImage()">
        <div class="share-option-icon">
          <ion-icon name="image-outline"></ion-icon>
        </div>
        <div class="share-option-text">
          <h3>{{ getImageOptionTitle() }}</h3>
          <p>{{ getImageOptionDescription() }}</p>
        </div>
      </div>
      <div class="share-option" (click)="onShareAsText()">
        <div class="share-option-icon">
          <ion-icon name="text-outline"></ion-icon>
        </div>
        <div class="share-option-text">
          <h3>{{ getTextOptionTitle() }}</h3>
          <p>{{ getTextOptionDescription() }}</p>
        </div>
      </div>
    </div>
  </div>
</div>