/* Modal de partage - Style amélioré avec animation du bas */
.share-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-end; /* <PERSON><PERSON><PERSON> en bas */
  z-index: 1000;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  animation: overlayFadeIn 0.3s ease-out;
}

@keyframes overlayFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.share-modal {
  width: 100%;
  max-width: 500px;
  background-color: var(--ion-background-color);
  border-radius: 16px 16px 0 0; /* <PERSON><PERSON><PERSON><PERSON> uniquement les coins supérieurs */
  box-shadow: 0 -4px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: modalSlideUp 0.3s ease-out;
  direction: ltr !important;
  text-align: left !important;
  transform-origin: bottom center;
}

@keyframes modalSlideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

.share-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.share-modal-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

/* Styles pour le bouton de fermeture */
.close-button {
  --padding-start: 8px;
  --padding-end: 8px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  margin: 0;
  height: 36px;
  width: 36px;
  --border-radius: 50%;
}

/* S'assurer que l'icône de fermeture est visible en mode sombre */
:host-context(body.dark) .close-button,
:host-context(.dark-1) .close-button,
:host-context(.dark-2) .close-button {
  --color: #FDF5E8; /* Couleur beige claire pour contraster avec le fond vert */
}

.share-options {
  padding: 16px;
}

.share-option {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 12px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.share-option:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.share-option:active {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Styles pour les icônes du modal de partage */
.share-option-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 56px;
  height: 56px;
  border-radius: 16px;
  margin-right: 20px;
}

/* Mode clair - couleur unie légèrement plus foncée que le fond */
.share-option-icon {
  background-color: #f0e8d8; /* Version plus foncée du beige de fond */
  color: #003C44;
}

.share-option-icon ion-icon {
  font-size: 28px;
  color: #003C44;
}

/* Mode sombre - couleur unie légèrement plus claire que le fond */
:host-context(.dark-1) .share-option-icon,
:host-context(.dark-2) .share-option-icon,
:host-context(body.dark) .share-option-icon {
  background-color: #004a55; /* Version plus claire du vert de fond */
  color: #FDF5E8;
}

:host-context(.dark-1) .share-option-icon ion-icon,
:host-context(.dark-2) .share-option-icon ion-icon,
:host-context(body.dark) .share-option-icon ion-icon {
  color: #FDF5E8;
}

.share-option-text {
  flex: 1;
}

.share-option-text h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.share-option-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.7;
}
