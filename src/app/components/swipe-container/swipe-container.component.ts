import { Component, Input, Output, EventEmitter, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonIcon } from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { bookmark } from 'ionicons/icons';

@Component({
  selector: 'app-swipe-container',
  templateUrl: './swipe-container.component.html',
  styleUrls: ['./swipe-container.component.scss'],
  standalone: true,
  imports: [CommonModule, IonIcon]
})
export class SwipeContainerComponent {
  @ViewChild('swipeContainer') swipeContainer!: ElementRef;
  @Input() currentHadith: any;
  @Input() currentLanguage: string = 'fr';
  @Input() canGoBack: boolean = false;
  @Input() showBookmarkAnimation: boolean = false;
  @Input() connectionError: boolean = false;
  @Output() leftZoneClick = new EventEmitter<void>();
  @Output() rightZoneClick = new EventEmitter<void>();
  
  constructor() {
    addIcons({ bookmark });
  }
  
  handleLeftZoneClick() {
    this.leftZoneClick.emit();
  }
  
  handleRightZoneClick() {
    this.rightZoneClick.emit();
  }
  
  getSwipeContainerElement(): ElementRef {
    return this.swipeContainer;
  }
}