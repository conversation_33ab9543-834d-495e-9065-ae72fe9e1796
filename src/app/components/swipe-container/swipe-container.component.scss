/* Structure de base */
.swipe-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow-x: hidden;
  pointer-events: auto !important;
}

/* Zones de clic pour la navigation */
.click-zone {
  position: absolute;
  top: 0;
  height: 100%;
  width: 30%;
  z-index: 5;
}

.click-zone-left {
  left: 0;
}

.click-zone-right {
  right: 0;
}

/* Contenu du hadith */
.hadith-content {
  width: 100%;
  max-width: 800px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 1rem;
  z-index: 10;
  position: relative;
}

/* Note: Les noms des classes CSS sont conservés pour compatibilité,
   mais les données correspondent maintenant à:
   - .sanad -> title (titre)
   - .matn -> hadeeth (texte principal)
   - .rawi -> attribution (attribution/source)
*/

/* <PERSON><PERSON> (maintenant title) */
.sanad {
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  line-height: 1.6;
  opacity: 0.8;
}

/* Matn (maintenant hadeeth) */
.matn-container {
  flex: 1;
  overflow-y: auto;
  margin: 1rem 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.matn {
  font-size: 1.4rem;
  line-height: 1.8;
  width: 100%;

  /* Alignement du texte selon la langue */
  &[lang="ar"] {
    text-align: right;
    direction: rtl;
  }

  &[lang="fr"], &[lang="en"] {
    text-align: left;
    direction: ltr;
  }
}

/* Rawi (maintenant attribution) */
.rawi {
  text-align: center;
  margin-top: 1.5rem;
  font-size: 1.1rem;
  line-height: 1.6;
  opacity: 0.8;
}

/* Animation de bookmark */
.bookmark-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
  animation: bookmarkPulse 1s ease-out;
  opacity: 0;
  pointer-events: none;
}

.bookmark-animation ion-icon {
  font-size: 5rem;
  color: var(--ion-color-primary);
}

@keyframes bookmarkPulse {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

/* Mais permettre les événements sur les éléments enfants */
.hadith-content, .click-zone, .matn-container, .matn {
  pointer-events: auto !important;
  touch-action: auto !important;
}

/* Centrer verticalement le matn quand il n'est pas scrollable */
.matn-container:not(.has-overflow) {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mais pour les conteneurs avec overflow, aligner en haut */
.matn-container.has-overflow {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

/* Gestion du débordement pour les grands hadiths */
.matn-container {
  max-height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  pointer-events: auto !important;

  /* Masquer la scrollbar pour Chrome, Safari et Opera */
  &::-webkit-scrollbar {
    display: none;
  }
}

/* Font adjustments for Arabic */
:host-context([lang="ar"]) {
  .sanad, .matn, .rawi {
    font-family: 'Amiri', serif !important;
  }

  .sanad {
    margin-bottom: 2rem !important; /* Increase spacing between sanad and matn */
  }

  .matn {
    font-size: 2.2rem !important;
    line-height: 1.8 !important;
  }
}

/* LTR support for French and English */
:host-context([dir="ltr"]) {
  .sanad, .matn, .rawi {
    direction: ltr;
    text-align: left;
  }
}
