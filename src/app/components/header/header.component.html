<ion-header [translucent]="true" class="ion-no-border">
  <ion-toolbar>
    <div class="flex items-center justify-between px-4">
      <div class="flex items-center">
        <img src="assets/icon/favicon.png" alt="Sihaah Logo" class="app-logo h-8 w-8">
        <ion-title class="font-medium">
          <span *ngIf="currentLanguage === 'ar'">صحاح</span>
          <span *ngIf="currentLanguage !== 'ar'">{{ title }}</span>
        </ion-title>
      </div>
      <div>
        <ion-button fill="clear" (click)="shareHadith($event)" class="header-button">
          <ion-icon name="share-social-outline"></ion-icon>
        </ion-button>
        <ion-button fill="clear" (click)="toggleTheme($event)" class="header-button">
          <ion-icon [name]="themeService.isDarkMode() ? 'sunny-outline' : 'moon-outline'"></ion-icon>
        </ion-button>
      </div>
    </div>
  </ion-toolbar>
</ion-header>
