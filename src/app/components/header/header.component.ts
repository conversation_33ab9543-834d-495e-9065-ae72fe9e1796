import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonHeader, IonToolbar, IonTitle, IonButton, IonIcon } from '@ionic/angular/standalone';
import { ThemeService } from '../../services/theme.service';
import { HadithService } from '../../services/hadith.service';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  standalone: true,
  imports: [CommonModule, IonHeader, IonToolbar, IonTitle, IonButton, IonIcon]
})
export class HeaderComponent implements OnInit {
  @Input() title: string = 'Sihaah';
  currentLanguage: string = 'ar';

  constructor(
    public themeService: ThemeService,
    private hadithService: HadithService
  ) {}

  ngOnInit() {
    // Initialiser la langue actuelle
    this.currentLanguage = this.hadithService.getLanguage();

    // S'abonner aux changements de langue
    this.hadithService.languageChanged.subscribe(lang => {
      this.currentLanguage = lang;
    });
  }

  toggleTheme(event: Event) {
    event.stopPropagation();
    this.themeService.toggleTheme();
  }

  shareHadith(event: Event) {
    event.stopPropagation();
    // Émettre un événement pour le composant parent
    const customEvent = new CustomEvent('shareHadith', { detail: event });
    window.dispatchEvent(customEvent);
  }
}
