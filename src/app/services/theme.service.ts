import { Injectable } from '@angular/core';
import { Preferences } from '@capacitor/preferences';

export type ThemeOption = 'light' | 'dark';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private currentTheme: ThemeOption = 'light';

  constructor() {
    this.loadSavedTheme();
  }

  async loadSavedTheme() {
    const { value } = await Preferences.get({ key: 'theme' });
    if (value && (value === 'light' || value === 'dark')) {
      this.setTheme(value as ThemeOption);
    } else {
      this.setTheme('light');
    }
  }

  async setTheme(theme: ThemeOption) {
    this.currentTheme = theme;

    // Remove all theme classes
    document.body.classList.remove('light-1', 'light-2', 'light-3', 'light-4', 'dark-1', 'dark-2', 'light', 'dark');

    // Add selected theme class
    document.body.classList.add(theme);

    // Set dark mode for Ionic
    if (theme === 'dark') {
      document.body.classList.add('dark');
    } else {
      document.body.classList.remove('dark');
    }

    // Save preference
    await Preferences.set({
      key: 'theme',
      value: theme
    });
  }

  getCurrentTheme(): ThemeOption {
    return this.currentTheme;
  }

  isDarkMode(): boolean {
    return this.currentTheme === 'dark';
  }

  toggleTheme() {
    const newTheme = this.isDarkMode() ? 'light' : 'dark';
    this.setTheme(newTheme);
  }
}
