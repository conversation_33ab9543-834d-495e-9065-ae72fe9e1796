import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { Preferences } from '@capacitor/preferences';

export type FontSize = 'small' | 'medium' | 'large' | 'x-large';

@Injectable({
  providedIn: 'root'
})
export class FontSizeService {
  private readonly FONT_SIZE_KEY = 'fontSizePreference';
  private fontSizeSubject = new BehaviorSubject<FontSize>('medium');
  
  fontSize$ = this.fontSizeSubject.asObservable();

  constructor() {
    this.loadSavedFontSize();
  }

  async loadSavedFontSize() {
    try {
      const { value } = await Preferences.get({ key: this.FONT_SIZE_KEY });
      if (value) {
        this.fontSizeSubject.next(value as FontSize);
      }
    } catch (error) {
      console.error('Error loading font size preference:', error);
    }
  }

  async setFontSize(size: FontSize) {
    try {
      await Preferences.set({
        key: this.FONT_SIZE_KEY,
        value: size
      });
      this.fontSizeSubject.next(size);
    } catch (error) {
      console.error('Error saving font size preference:', error);
    }
  }

  getCurrentFontSize(): FontSize {
    return this.fontSizeSubject.value;
  }

  getNextFontSize(): FontSize {
    const sizes: FontSize[] = ['small', 'medium', 'large', 'x-large'];
    const currentIndex = sizes.indexOf(this.fontSizeSubject.value);
    const nextIndex = (currentIndex + 1) % sizes.length;
    return sizes[nextIndex];
  }

  // Obtenir les classes CSS pour la taille de police actuelle
  getFontSizeClass(): string {
    return `font-size-${this.fontSizeSubject.value}`;
  }

  // Obtenir les tailles réelles en pixels pour chaque option
  getFontSizePixels(language: string): { matn: string, sanad: string, rawi: string } {
    const isArabic = language === 'ar';
    
    switch (this.fontSizeSubject.value) {
      case 'small':
        return {
          matn: isArabic ? '1.8rem' : '1.2rem',
          sanad: isArabic ? '1.1rem' : '1rem',
          rawi: isArabic ? '1rem' : '0.9rem'
        };
      case 'medium':
        return {
          matn: isArabic ? '2.2rem' : '1.5rem',
          sanad: isArabic ? '1.25rem' : '1.125rem',
          rawi: isArabic ? '1.125rem' : '1rem'
        };
      case 'large':
        return {
          matn: isArabic ? '2.6rem' : '1.8rem',
          sanad: isArabic ? '1.4rem' : '1.25rem',
          rawi: isArabic ? '1.25rem' : '1.1rem'
        };
      case 'x-large':
        return {
          matn: isArabic ? '3rem' : '2.1rem',
          sanad: isArabic ? '1.6rem' : '1.4rem',
          rawi: isArabic ? '1.4rem' : '1.2rem'
        };
      default:
        return {
          matn: isArabic ? '2.2rem' : '1.5rem',
          sanad: isArabic ? '1.25rem' : '1.125rem',
          rawi: isArabic ? '1.125rem' : '1rem'
        };
    }
  }
}