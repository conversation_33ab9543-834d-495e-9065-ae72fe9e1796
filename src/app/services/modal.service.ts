import { Injectable } from '@angular/core';
import { ModalController } from '@ionic/angular/standalone';
import { FontSizeModalComponent } from '../components/font-size-modal/font-size-modal.component';

@Injectable({
  providedIn: 'root'
})
export class ModalService {
  constructor(private modalController: ModalController) {}

  async openFontSizeModal() {
    const modal = await this.modalController.create({
      component: FontSizeModalComponent,
      cssClass: 'font-size-modal'
    });

    await modal.present();
    return modal.onDidDismiss();
  }
}