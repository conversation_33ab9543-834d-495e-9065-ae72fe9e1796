import { Injectable, EventEmitter } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Preferences } from '@capacitor/preferences';
import { firstValueFrom } from 'rxjs';
import localforage from 'localforage';

export interface Hadith {
  id: number;
  translations: {
    [key: string]: {
      title: string;
      hadeeth: string;
      attribution: string;
    }
  };
}

@Injectable({
  providedIn: 'root'
})
export class HadithService {
  private hadiths: Hadith[] = [];
  private viewedHadiths: number[] = [];
  private hadithHistory: number[] = []; // Historique des hadiths affichés
  private historyIndex: number = -1; // Index actuel dans l'historique
  private currentLanguage = 'ar'; // Set Arabic as default
  // Nouvelle URL de l'API
  private readonly API_BASE_URL = 'https://hadeethenc.com/api/v1/hadeeths/one/';
  // Liste des IDs valides (à remplir)
  private validHadithIds: number[] = [];
  private hadithsLoaded = false;
  private currentBatchIndex = 0;
  private readonly BATCH_SIZE = 20;
  private loadingBatch = false;
  // Propriété pour stocker l'ID du hadith à afficher
  private hadithToView: number | null = null;
  // Nombre maximum de hadiths à garder en cache
  private readonly MAX_CACHED_HADITHS = 300;

  // Événements existants
  languageChanged = new EventEmitter<string>();
  hadithChanged = new EventEmitter<Hadith>();
  networkError = new EventEmitter<boolean>();
  // Nouvel événement pour indiquer le chargement d'un nouveau lot
  batchLoading = new EventEmitter<boolean>();

  constructor(private http: HttpClient) {
    // Initialiser localforage
    localforage.config({
      name: 'sihaah',
      storeName: 'hadiths',
      description: 'Stockage local des hadiths'
    });

    this.loadValidHadithIds();
    this.loadViewedHadiths();
    this.loadLanguagePreference();
    this.loadHadithHistory();
  }

  // Méthode pour charger les IDs valides
  private async loadValidHadithIds() {
    try {
      // D'abord essayer de charger depuis le stockage local
      const cachedIds = await localforage.getItem<number[]>('validHadithIds');

      if (cachedIds && cachedIds.length > 0) {
        console.log('Valid hadith IDs loaded from cache, count:', cachedIds.length);
        this.validHadithIds = cachedIds;
        this.loadInitialBatch();
      } else {
        // Si pas en cache, charger depuis l'API
        const response = await firstValueFrom(this.http.get<number[]>('assets/valid-hadith-ids.json'));
        this.validHadithIds = response;
        console.log('Valid hadith IDs loaded from API, count:', this.validHadithIds.length);

        // Sauvegarder en cache
        await localforage.setItem('validHadithIds', this.validHadithIds);

        this.loadInitialBatch();
      }
    } catch (error) {
      console.error('Error loading valid hadith IDs:', error);
      this.networkError.emit(true);
    }
  }

  // Charger le premier lot de hadiths
  private async loadInitialBatch() {
    try {
      this.hadithsLoaded = false;
      console.log('Loading initial batch of hadiths...');

      // Charger les hadiths depuis le cache d'abord
      const cachedHadiths = await this.loadHadithsFromCache();

      if (cachedHadiths.length > 0) {
        console.log('Loaded hadiths from cache, count:', cachedHadiths.length);
        this.hadiths = cachedHadiths;
        this.hadithsLoaded = true;
        this.networkError.emit(false);

        // Déterminer le batch index actuel basé sur les hadiths en cache
        const maxId = Math.max(...this.hadiths.map(h => h.id));
        const maxIdIndex = this.validHadithIds.indexOf(maxId);
        if (maxIdIndex !== -1) {
          this.currentBatchIndex = Math.floor(maxIdIndex / this.BATCH_SIZE) + 1;
          console.log('Current batch index set to:', this.currentBatchIndex);
        }

        // Précharger le prochain lot en arrière-plan si nécessaire
        this.preloadNextBatchIfNeeded();
      } else {
        // Si rien en cache, charger depuis l'API
        await this.loadNextBatch();
        this.hadithsLoaded = true;
        this.networkError.emit(false);
      }
    } catch (error) {
      console.error('Error loading initial batch:', error);
      this.hadithsLoaded = true;
      this.networkError.emit(true);
    }
  }

  // Charger les hadiths depuis le cache
  private async loadHadithsFromCache(): Promise<Hadith[]> {
    try {
      const cachedHadiths = await localforage.getItem<Hadith[]>('cachedHadiths');
      return cachedHadiths || [];
    } catch (error) {
      console.error('Error loading hadiths from cache:', error);
      return [];
    }
  }

  // Sauvegarder les hadiths en cache
  private async saveHadithsToCache(hadiths: Hadith[]) {
    try {
      // Limiter le nombre de hadiths en cache
      let allCachedHadiths = await this.loadHadithsFromCache();

      // Ajouter les nouveaux hadiths
      const newHadithIds = hadiths.map(h => h.id);
      allCachedHadiths = allCachedHadiths.filter(h => !newHadithIds.includes(h.id));
      allCachedHadiths = [...allCachedHadiths, ...hadiths];

      // Si on dépasse la limite, supprimer les plus anciens
      if (allCachedHadiths.length > this.MAX_CACHED_HADITHS) {
        allCachedHadiths = allCachedHadiths.slice(allCachedHadiths.length - this.MAX_CACHED_HADITHS);
      }

      await localforage.setItem('cachedHadiths', allCachedHadiths);
      console.log('Saved hadiths to cache, total count:', allCachedHadiths.length);
    } catch (error) {
      console.error('Error saving hadiths to cache:', error);
    }
  }

  // Charger le prochain lot de hadiths
  async loadNextBatch(): Promise<boolean> {
    if (this.loadingBatch || this.currentBatchIndex * this.BATCH_SIZE >= this.validHadithIds.length) {
      return false;
    }

    this.loadingBatch = true;
    this.batchLoading.emit(true);

    try {
      const startIndex = this.currentBatchIndex * this.BATCH_SIZE;
      const endIndex = Math.min(startIndex + this.BATCH_SIZE, this.validHadithIds.length);
      const batchIds = this.validHadithIds.slice(startIndex, endIndex);

      console.log(`Loading batch ${this.currentBatchIndex + 1}, IDs ${startIndex} to ${endIndex - 1}`);

      // Vérifier quels IDs sont déjà en cache
      const cachedHadiths = await this.loadHadithsFromCache();
      const cachedIds = cachedHadiths.map(h => h.id);
      const idsToFetch = batchIds.filter(id => !cachedIds.includes(id));

      let newHadiths: Hadith[] = [];

      // Récupérer les hadiths déjà en cache
      const hadithsFromCache = cachedHadiths.filter(h => batchIds.includes(h.id));
      newHadiths = [...hadithsFromCache];

      // Récupérer les hadiths manquants depuis l'API
      if (idsToFetch.length > 0) {
        console.log(`Fetching ${idsToFetch.length} hadiths from API`);

        try {
          const hadithsFromAPI = await this.fetchHadithsByIds(idsToFetch);

          // Ajouter les hadiths récupérés depuis l'API
          newHadiths = [...newHadiths, ...hadithsFromAPI];

          // Sauvegarder les nouveaux hadiths en cache
          await this.saveHadithsToCache(hadithsFromAPI);
        } catch (error) {
          console.error('Error fetching hadiths from API:', error);
          // Si nous avons des hadiths du cache, continuer sans émettre d'erreur
          if (newHadiths.length === 0) {
            throw error; // Relancer l'erreur seulement si aucun hadith n'est disponible
          }
        }
      }

      // Vérifier si des hadiths ont été chargés
      if (newHadiths.length === 0) {
        console.error('No hadiths loaded in batch');
        throw new Error('No hadiths loaded in batch');
      }

      // Ajouter les nouveaux hadiths à la liste existante (sans doublons)
      const existingIds = this.hadiths.map(h => h.id);
      const uniqueNewHadiths = newHadiths.filter(h => !existingIds.includes(h.id));
      this.hadiths = [...this.hadiths, ...uniqueNewHadiths];

      this.currentBatchIndex++;
      console.log(`Batch loaded. Total hadiths: ${this.hadiths.length}`);

      this.loadingBatch = false;
      this.batchLoading.emit(false);
      return true;
    } catch (error) {
      console.error('Error loading next batch:', error);
      this.loadingBatch = false;
      this.batchLoading.emit(false);
      this.networkError.emit(true);
      return false;
    }
  }

  // Précharger le prochain lot si nécessaire
  async preloadNextBatchIfNeeded() {
    // Si nous sommes près de la fin du lot actuel, précharger le suivant
    const currentPosition = this.viewedHadiths.length % this.BATCH_SIZE;
    const nearEndOfBatch = currentPosition >= this.BATCH_SIZE - 3; // À 3 hadiths de la fin

    if (nearEndOfBatch && !this.loadingBatch) {
      console.log('Near end of batch, preloading next batch');
      this.loadNextBatch().then(success => {
        if (success) {
          console.log('Next batch preloaded successfully');
        }
      });
    }
  }

  // Méthode pour recharger tous les hadiths (utilisée pour les retries)
  async loadHadiths() {
    try {
      this.hadithsLoaded = false;
      console.log('Reloading hadiths...');

      // D'abord essayer de charger depuis le cache
      const cachedHadiths = await this.loadHadithsFromCache();

      if (cachedHadiths.length > 0) {
        console.log('Loaded hadiths from cache, count:', cachedHadiths.length);
        this.hadiths = cachedHadiths;
        this.hadithsLoaded = true;
        this.networkError.emit(false);
        return true;
      }

      // Si rien en cache, réinitialiser et charger le premier lot
      this.hadiths = [];
      this.currentBatchIndex = 0;

      // Attendre un peu plus longtemps avant de considérer qu'il y a une erreur
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout loading hadiths')), 15000)
      );

      // Essayer de charger le premier lot avec un timeout
      await Promise.race([
        this.loadNextBatch(),
        timeoutPromise
      ]);

      this.hadithsLoaded = true;

      if (this.hadiths.length > 0) {
        this.networkError.emit(false);
        return true;
      } else {
        this.networkError.emit(true);
        return false;
      }
    } catch (error) {
      console.error('Error reloading hadiths:', error);
      this.hadithsLoaded = true;
      this.networkError.emit(true);
      return false;
    }
  }

  // Récupérer des hadiths par IDs depuis l'API
  private async fetchHadithsByIds(ids: number[]): Promise<Hadith[]> {
    const results: Hadith[] = [];

    for (const id of ids) {
      try {
        // Récupérer les données pour chaque langue en parallèle
        const [arData, frData, enData] = await Promise.all([
          firstValueFrom(this.http.get<any>(`${this.API_BASE_URL}?language=ar&id=${id}`)),
          firstValueFrom(this.http.get<any>(`${this.API_BASE_URL}?language=fr&id=${id}`)),
          firstValueFrom(this.http.get<any>(`${this.API_BASE_URL}?language=en&id=${id}`))
        ]);

        results.push({
          id,
          translations: {
            ar: {
              title: arData.title || '',
              hadeeth: arData.hadeeth || '',
              attribution: arData.attribution || ''
            },
            fr: {
              title: frData.title || '',
              hadeeth: frData.hadeeth || '',
              attribution: frData.attribution || ''
            },
            en: {
              title: enData.title || '',
              hadeeth: enData.hadeeth || '',
              attribution: enData.attribution || ''
            }
          }
        });
      } catch (error) {
        console.error(`Error fetching hadith ID ${id}:`, error);
      }
    }

    return results;
  }

  async loadViewedHadiths() {
    const { value } = await Preferences.get({ key: 'viewedHadiths' });
    if (value) {
      this.viewedHadiths = JSON.parse(value);
    }
  }

  async saveViewedHadiths() {
    await Preferences.set({
      key: 'viewedHadiths',
      value: JSON.stringify(this.viewedHadiths)
    });
  }

  async loadLanguagePreference() {
    const { value } = await Preferences.get({ key: 'language' });
    if (value) {
      this.currentLanguage = value;
    } else {
      // If no preference is saved, set Arabic as default
      this.setLanguage('ar');
    }
  }

  getLanguage(): string {
    return this.currentLanguage;
  }

  setLanguage(lang: 'ar' | 'fr' | 'en') {
    this.currentLanguage = lang;
    // Save preference
    Preferences.set({
      key: 'language',
      value: lang
    });
    // Emit event
    this.languageChanged.emit(lang);
  }

  // Méthode pour obtenir le prochain hadith
  async getNextHadith(): Promise<Hadith | null> {
    console.log('getNextHadith called');

    // Si aucun hadith n'est disponible, retourner null
    if (this.hadiths.length === 0) {
      console.error('No hadiths available');
      return null;
    }

    // Vérifier si nous sommes déjà dans l'historique
    if (this.historyIndex < this.hadithHistory.length - 1) {
      // Si nous sommes dans l'historique, avancer simplement à l'élément suivant
      console.log('Moving forward in history');
      this.historyIndex++;
      const nextHadithId = this.hadithHistory[this.historyIndex];
      console.log('Next hadith ID from history:', nextHadithId);

      // Trouver le hadith correspondant
      let nextHadith = this.hadiths.find(h => h.id === nextHadithId);

      // Si le hadith n'est pas dans la liste chargée, essayer de le charger
      if (!nextHadith) {
        try {
          // D'abord vérifier dans le cache
          const cachedHadiths = await this.loadHadithsFromCache();
          nextHadith = cachedHadiths.find(h => h.id === nextHadithId);

          // Si pas en cache, charger depuis l'API
          if (!nextHadith) {
            const loadedHadiths = await this.fetchHadithsByIds([nextHadithId]);
            if (loadedHadiths.length > 0) {
              nextHadith = loadedHadiths[0];
              this.hadiths.push(nextHadith);
              // Sauvegarder en cache
              await this.saveHadithsToCache([nextHadith]);
            }
          } else {
            // Ajouter à la liste en mémoire
            this.hadiths.push(nextHadith);
          }
        } catch (error) {
          console.error(`Failed to load hadith ID ${nextHadithId}:`, error);
          this.networkError.emit(true);
          return null;
        }
      }

      if (!nextHadith) {
        console.error('Next hadith not found in hadiths array');
        return null;
      }

      console.log(`Returning to hadith #${nextHadithId} from history. Position: ${this.historyIndex + 1}/${this.hadithHistory.length}`);
      return nextHadith;
    }

    // Si nous sommes à la fin de l'historique, obtenir un nouveau hadith
    console.log('At end of history, getting new hadith');

    // Trouver les hadiths qui n'ont pas encore été vus
    let availableHadiths = this.hadiths.filter(h => !this.viewedHadiths.includes(h.id));
    console.log('Available hadiths in current batch:', availableHadiths.length);

    // Si tous les hadiths chargés ont été vus, essayer de charger un nouveau lot
    if (availableHadiths.length === 0) {
      // Vérifier si nous avons atteint la fin de tous les hadiths disponibles
      if (this.currentBatchIndex * this.BATCH_SIZE >= this.validHadithIds.length) {
        console.log('All hadiths have been viewed, resetting viewed list');
        // Réinitialiser la liste des hadiths vus pour recommencer
        this.viewedHadiths = [];
        await this.saveViewedHadiths();

        // Maintenant que la liste est réinitialisée, tous les hadiths sont disponibles
        availableHadiths = this.hadiths;
      } else {
        // Sinon, charger le prochain lot
        console.log('Loading next batch of hadiths');

        // Émettre un événement pour indiquer que nous chargeons un nouveau lot
        // mais ne pas encore émettre d'erreur réseau
        this.batchLoading.emit(true);

        const success = await this.loadNextBatch();

        this.batchLoading.emit(false);

        if (!success) {
          console.error('Failed to load next batch');
          // Ne pas émettre d'erreur réseau ici, car loadNextBatch l'a déjà fait si nécessaire
          return null;
        }

        // Après avoir chargé un nouveau lot, chercher à nouveau les hadiths disponibles
        availableHadiths = this.hadiths.filter(h => !this.viewedHadiths.includes(h.id));
      }
    }

    console.log('Available hadiths after checks:', availableHadiths.length);

    if (availableHadiths.length === 0) {
      console.error('No available hadiths found after all checks');
      return null;
    }

    // Obtenir un hadith aléatoire parmi ceux disponibles
    const randomIndex = Math.floor(Math.random() * availableHadiths.length);
    const selectedHadith = availableHadiths[randomIndex];
    console.log('Selected hadith:', selectedHadith.id);

    // Marquer comme vu
    this.viewedHadiths.push(selectedHadith.id);
    this.saveViewedHadiths();

    // Ajouter à l'historique et mettre à jour l'index
    this.addToHistory(selectedHadith.id);

    // Vérifier si nous devons précharger le prochain lot
    this.preloadNextBatchIfNeeded();

    console.log(`Hadith #${selectedHadith.id} displayed. Viewed hadiths: ${this.viewedHadiths.length}/${this.hadiths.length}`);
    console.log('Current history:', this.hadithHistory);
    console.log('Current history index:', this.historyIndex);

    return selectedHadith;
  }

  // Méthode pour obtenir le hadith précédent
  async getPreviousHadith(): Promise<Hadith | null> {
    console.log('getPreviousHadith called');
    console.log('History:', this.hadithHistory);
    console.log('History index:', this.historyIndex);

    // Si l'historique est vide ou si nous sommes au début de l'historique
    if (this.hadithHistory.length === 0 || this.historyIndex <= 0) {
      console.error('No previous hadith available');
      return null;
    }

    // Reculer dans l'historique
    this.historyIndex--;
    const previousHadithId = this.hadithHistory[this.historyIndex];
    console.log('Previous hadith ID:', previousHadithId);

    // Trouver le hadith correspondant
    let previousHadith = this.hadiths.find(h => h.id === previousHadithId);

    // Si le hadith n'est pas dans la liste chargée, essayer de le charger
    if (!previousHadith) {
      try {
        const loadedHadiths = await this.fetchHadithsByIds([previousHadithId]);
        if (loadedHadiths.length > 0) {
          previousHadith = loadedHadiths[0];
          this.hadiths.push(previousHadith);
        }
      } catch (error) {
        console.error(`Failed to load hadith ID ${previousHadithId}:`, error);
      }
    }

    if (!previousHadith) {
      console.error('Previous hadith not found in hadiths array');
      return null;
    }

    console.log(`Returning to hadith #${previousHadithId}. Position in history: ${this.historyIndex + 1}/${this.hadithHistory.length}`);

    return previousHadith;
  }

  // Ajouter un hadith à l'historique
  addToHistory(hadithId: number) {
    // Si nous ne sommes pas à la fin de l'historique, supprimer tout ce qui suit
    if (this.historyIndex < this.hadithHistory.length - 1) {
      this.hadithHistory = this.hadithHistory.slice(0, this.historyIndex + 1);
    }

    // Ajouter le nouvel ID à l'historique
    this.hadithHistory.push(hadithId);
    this.historyIndex = this.hadithHistory.length - 1;

    // Sauvegarder l'historique
    this.saveHadithHistory();

    console.log(`Hadith #${hadithId} added to history. Position: ${this.historyIndex + 1}/${this.hadithHistory.length}`);
    console.log('Updated history:', this.hadithHistory);
    console.log('Updated history index:', this.historyIndex);
  }

  // Sauvegarder l'historique des hadiths
  private async saveHadithHistory() {
    try {
      await localforage.setItem('hadithHistory', {
        history: this.hadithHistory,
        index: this.historyIndex
      });
    } catch (error) {
      console.error('Error saving hadith history:', error);
    }
  }

  // Charger l'historique des hadiths
  private async loadHadithHistory() {
    try {
      const savedHistory = await localforage.getItem<{
        history: number[],
        index: number
      }>('hadithHistory');

      if (savedHistory) {
        this.hadithHistory = savedHistory.history;
        this.historyIndex = savedHistory.index;
        console.log('Loaded hadith history from storage:', this.hadithHistory.length, 'items');
      }
    } catch (error) {
      console.error('Error loading hadith history:', error);
    }
  }

  // Ajouter une méthode pour obtenir le nombre de hadiths vus et le total
  getViewedHadithsStats(): { viewed: number, total: number } {
    return {
      viewed: this.viewedHadiths.length,
      total: this.hadiths.length
    };
  }

  // Vérifier si on peut revenir en arrière dans l'historique
  canGoBack(): boolean {
    return this.historyIndex > 0;
  }

  // Méthode pour obtenir un hadith spécifique par ID
  async getHadithById(id: number): Promise<Hadith | null> {
    // Vérifier d'abord si le hadith est déjà chargé
    let hadith = this.hadiths.find(h => h.id === id);

    // Si le hadith n'est pas chargé, essayer de le récupérer
    if (!hadith) {
      try {
        const loadedHadiths = await this.fetchHadithsByIds([id]);
        if (loadedHadiths.length > 0) {
          hadith = loadedHadiths[0];
          this.hadiths.push(hadith);
        }
      } catch (error) {
        console.error(`Failed to load hadith ID ${id}:`, error);
        return null;
      }
    }

    return hadith || null;
  }

  // Méthode pour obtenir plusieurs hadiths par leurs IDs
  async getHadithsByIds(ids: number[]): Promise<Hadith[]> {
    if (!ids || ids.length === 0) {
      return [];
    }

    console.log(`Getting ${ids.length} hadiths by IDs:`, ids);

    // Créer une copie des IDs pour éviter de modifier l'original
    const idsToProcess = [...ids];

    // Résultat final
    const result: Hadith[] = [];

    // 1. D'abord, chercher dans la liste en mémoire
    const foundInMemory: number[] = [];

    for (const id of idsToProcess) {
      const hadith = this.hadiths.find(h => h.id === id);
      if (hadith) {
        result.push(hadith);
        foundInMemory.push(id);
      }
    }

    // Filtrer les IDs trouvés
    let remainingIds = idsToProcess.filter(id => !foundInMemory.includes(id));

    // Si tous les hadiths ont été trouvés en mémoire, retourner le résultat
    if (remainingIds.length === 0) {
      console.log('All hadiths found in memory');
      return result;
    }

    // 2. Ensuite, chercher dans le cache
    try {
      const cachedHadiths = await this.loadHadithsFromCache();
      const foundInCache: number[] = [];

      for (const id of remainingIds) {
        const cachedHadith = cachedHadiths.find(h => h.id === id);
        if (cachedHadith) {
          result.push(cachedHadith);
          foundInCache.push(id);

          // Ajouter à la liste en mémoire pour les prochaines requêtes
          if (!this.hadiths.some(h => h.id === id)) {
            this.hadiths.push(cachedHadith);
          }
        }
      }

      // Mettre à jour la liste des IDs restants
      remainingIds = remainingIds.filter(id => !foundInCache.includes(id));

      // Si tous les hadiths ont été trouvés, retourner le résultat
      if (remainingIds.length === 0) {
        console.log('All remaining hadiths found in cache');
        return result;
      }

      // 3. Enfin, récupérer les hadiths restants depuis l'API
      console.log('Fetching remaining hadiths from API:', remainingIds);

      try {
        const fetchedHadiths = await this.fetchHadithsByIds(remainingIds);

        // Ajouter les hadiths récupérés au résultat
        result.push(...fetchedHadiths);

        // Ajouter à la liste en mémoire
        for (const hadith of fetchedHadiths) {
          if (!this.hadiths.some(h => h.id === hadith.id)) {
            this.hadiths.push(hadith);
          }
        }

        // Sauvegarder en cache
        await this.saveHadithsToCache(fetchedHadiths);
      } catch (error) {
        console.error('Error fetching hadiths from API:', error);
        // Continuer avec les hadiths déjà récupérés
      }
    } catch (error) {
      console.error('Error accessing cache:', error);
      // Continuer avec les hadiths déjà récupérés
    }

    console.log(`Returning ${result.length} hadiths out of ${ids.length} requested`);
    return result;
  }

  // Méthode pour forcer le chargement d'un hadith spécifique
  async forceLoadHadith(hadithId: number): Promise<void> {
    // Trouver le hadith correspondant ou le charger si nécessaire
    const hadith = await this.getHadithById(hadithId);

    if (hadith) {
      // Ajouter à l'historique
      this.addToHistory(hadithId);

      // Émettre un événement pour informer les composants
      this.hadithChanged.emit(hadith);
    }
  }

  // Méthode pour vérifier si les hadiths sont chargés
  isHadithsLoaded(): boolean {
    return this.hadithsLoaded && this.hadiths.length > 0;
  }

  // Méthode pour obtenir le nombre de hadiths chargés
  getHadithsCount(): number {
    return this.hadiths.length;
  }

  // Attendre que les hadiths soient chargés
  async waitForHadithsLoaded(): Promise<void> {
    if (this.hadithsLoaded && this.hadiths.length > 0) {
      return Promise.resolve();
    }

    try {
      // Attendre que le chargement initial soit terminé avec un timeout plus long
      const maxWaitTime = 30000; // 30 secondes
      const startTime = Date.now();

      while (!this.hadithsLoaded && this.hadiths.length === 0) {
        // Vérifier si on a dépassé le temps d'attente maximum
        if (Date.now() - startTime > maxWaitTime) {
          console.error('Timeout waiting for hadiths to load');
          throw new Error('Timeout waiting for hadiths to load');
        }

        // Attendre 200ms avant de vérifier à nouveau
        await new Promise(resolve => setTimeout(resolve, 200));

        // Afficher des logs de progression
        if ((Date.now() - startTime) % 5000 < 200) {
          console.log(`Still waiting for hadiths to load... (${Math.floor((Date.now() - startTime)/1000)}s)`);
        }
      }

      // Vérifier si des hadiths ont été chargés
      if (this.hadiths.length === 0) {
        console.error('No hadiths loaded after waiting');
        throw new Error('No hadiths loaded');
      }

      console.log(`Hadiths loaded successfully after ${Math.floor((Date.now() - startTime)/1000)}s`);
      return Promise.resolve();
    } catch (error) {
      console.error('Error waiting for hadiths to load:', error);
      // Marquer comme chargé même en cas d'erreur pour éviter de bloquer l'application
      this.hadithsLoaded = true;
      throw error;
    }
  }

  // Méthode pour obtenir l'ID du hadith actuellement affiché
  getCurrentHadithId(): number | null {
    // Si l'historique est vide, retourner null
    if (this.hadithHistory.length === 0 || this.historyIndex < 0) {
      return null;
    }

    // Retourner l'ID du hadith actuel
    return this.hadithHistory[this.historyIndex];
  }

  // Méthode pour définir un hadith spécifique à afficher
  setHadithToView(hadithId: number) {
    this.hadithToView = hadithId;
  }

  // Méthode pour récupérer et effacer le hadith à afficher
  getHadithToView(): number | null {
    const id = this.hadithToView;
    this.hadithToView = null;
    return id;
  }

  // Méthode pour charger un hadith aléatoire non vu
  async loadRandomUnseenHadith(): Promise<Hadith | null> {
    console.log('loadRandomUnseenHadith called');

    // Si nous n'avons pas encore chargé les IDs valides, attendre
    if (this.validHadithIds.length === 0) {
      console.log('Waiting for valid hadith IDs to load');
      await new Promise(resolve => {
        const checkInterval = setInterval(() => {
          if (this.validHadithIds.length > 0) {
            clearInterval(checkInterval);
            resolve(true);
          }
        }, 500);

        // Timeout après 10 secondes
        setTimeout(() => {
          clearInterval(checkInterval);
          resolve(false);
        }, 10000);
      });

      if (this.validHadithIds.length === 0) {
        console.error('Failed to load valid hadith IDs');
        return null;
      }
    }

    // Trouver les IDs qui n'ont pas encore été vus
    const unseenIds = this.validHadithIds.filter(id => !this.viewedHadiths.includes(id));
    console.log('Unseen hadith IDs:', unseenIds.length);

    // Si tous les hadiths ont été vus, réinitialiser la liste
    if (unseenIds.length === 0) {
      console.log('All hadiths have been viewed, resetting viewed list');
      this.viewedHadiths = [];
      await this.saveViewedHadiths();
      // Maintenant tous les IDs sont disponibles
      const randomId = this.validHadithIds[Math.floor(Math.random() * this.validHadithIds.length)];
      return this.getHadithById(randomId);
    }

    // Sélectionner un ID aléatoire parmi ceux non vus
    const randomId = unseenIds[Math.floor(Math.random() * unseenIds.length)];
    console.log('Selected random unseen hadith ID:', randomId);

    // Charger ce hadith
    const hadith = await this.getHadithById(randomId);

    if (hadith) {
      // Marquer comme vu
      this.viewedHadiths.push(randomId);
      this.saveViewedHadiths();

      // Ajouter à l'historique
      this.addToHistory(randomId);

      return hadith;
    }

    return null;
  }
}
