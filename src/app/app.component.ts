import { Component, OnInit } from '@angular/core';
import { HadithService } from './services/hadith.service';
import { StatusBar, Style } from '@capacitor/status-bar';
import { SplashScreen } from '@capacitor/splash-screen';
import { IonApp, IonRouterOutlet, Platform } from '@ionic/angular/standalone';
import { App } from '@capacitor/app';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { Toast } from '@capacitor/toast';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  imports: [
    IonApp,
    IonRouterOutlet
  ]
})
export class AppComponent implements OnInit {
  private lastTimeBackPressed = 0;
  private timePeriodToExit = 2000;
  private currentRoute = '';

  constructor(
    private hadithService: HadithService,
    private platform: Platform,
    private router: Router
  ) {
    this.initializeApp();
  }

  async initializeApp() {
    await this.platform.ready();

    try {
      // Vérifier si l'application s'exécute sur une plateforme mobile
      if (this.platform.is('capacitor')) {
        // Configure StatusBar - uniquement sur mobile
        await StatusBar.setStyle({ style: Style.Dark });
        if (this.platform.is('android')) {
          StatusBar.setBackgroundColor({ color: '#FDF5E8' });
        }

        // Hide splash screen - uniquement sur mobile
        await SplashScreen.hide();

        // Gérer le bouton retour du téléphone
        App.addListener('backButton', () => {
          if (this.currentRoute === '/home') {
            // Si on est sur la page d'accueil, double-clic pour quitter l'application
            if (new Date().getTime() - this.lastTimeBackPressed < this.timePeriodToExit) {
              App.exitApp();
            } else {
              this.lastTimeBackPressed = new Date().getTime();
              // Afficher un toast "Appuyez à nouveau pour quitter"
              Toast.show({
                text: this.hadithService.getLanguage() === 'ar' ? 'اضغط مرة أخرى للخروج' :
                  this.hadithService.getLanguage() === 'fr' ? 'Appuyez à nouveau pour quitter' :
                    'Press again to exit',
                duration: 'short',
                position: 'bottom'
              });
            }
          } else {
            // Si on n'est pas sur la page d'accueil, revenir à la page d'accueil
            this.router.navigate(['/home']);
          }
        });
      }
    } catch (err) {
      console.warn('Error initializing app:', err);
    }
  }

  ngOnInit() {
    // Set RTL direction for Arabic language
    this.updateDirection(this.hadithService.getLanguage());

    // Listen for language changes
    this.hadithService.languageChanged.subscribe(lang => {
      this.updateDirection(lang);
    });

    // Suivre la route actuelle
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: any) => {
      this.currentRoute = event.url;
    });
  }

  private updateDirection(lang: string) {
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = lang;
  }
}
