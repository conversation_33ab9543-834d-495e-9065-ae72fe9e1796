/* Make sure Ionic doesn't override our background */
:root {
  --ion-background-color: transparent;

  /* Set brand colors for Ionic components */
  --ion-color-primary: #003C44;
  --ion-color-primary-rgb: 0, 60, 68;
  --ion-color-primary-contrast: #FDF5E8;
  --ion-color-primary-contrast-rgb: 253, 245, 232;
  --ion-color-primary-shade: #00353c;
  --ion-color-primary-tint: #1a5057;

  --ion-color-secondary: #FDF5E8;
  --ion-color-secondary-rgb: 253, 245, 232;
  --ion-color-secondary-contrast: #003C44;
  --ion-color-secondary-contrast-rgb: 0, 60, 68;
  --ion-color-secondary-shade: #dfd8cc;
  --ion-color-secondary-tint: #fdf6ea;
}

.ion-color-primary {
  --ion-color-base: var(--ion-color-primary, #003C44);
  --ion-color-base-rgb: var(--ion-color-primary-rgb, 0, 60, 68);
  --ion-color-contrast: var(--ion-color-primary-contrast, #FDF5E8);
  --ion-color-contrast-rgb: var(--ion-color-primary-contrast-rgb, 253, 245, 232);
  --ion-color-shade: var(--ion-color-primary-shade, #00353c);
  --ion-color-tint: var(--ion-color-primary-tint, #1a5057);
}

.ion-color-secondary {
  --ion-color-base: var(--ion-color-secondary, #FDF5E8);
  --ion-color-base-rgb: var(--ion-color-secondary-rgb, 253, 245, 232);
  --ion-color-contrast: var(--ion-color-secondary-contrast, #003C44);
  --ion-color-contrast-rgb: var(--ion-color-secondary-contrast-rgb, 0, 60, 68);
  --ion-color-shade: var(--ion-color-secondary-shade, #dfd8cc);
  --ion-color-tint: var(--ion-color-secondary-tint, #fdf6ea);
}
